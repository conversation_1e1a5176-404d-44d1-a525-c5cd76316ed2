<?php

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Auth::routes();

Route::group(['middleware' => 'auth'], function () {

    Route::get('/change/password','Auth\ChangePasswordController@showChangePasswordForm')->name('changePasswordLink');
    Route::post('/change/password','Auth\ChangePasswordController@changePassword')->name('changePassword');

    Route::group(['middleware' => 'admin', 'as' => 'admin.', 'prefix' => 'admin'], function () {
        /**
         * Dashboard
         */
        Route::get('/', [
            'as' => 'admin_dashboard',
            'uses' => 'Admin\DashboardController@index'
        ]);

        Route::any('/bazaarvoice', [
            'as' => 'bazaarvoice',
            'uses' => 'Admin\DashboardController@bazaarvoice'
        ]);

        Route::any('/crawler', [
            'as' => 'crawler',
            'uses' => 'Admin\DashboardController@crawl'
        ]);

        /**
         * User | Group | Permission
         */
        Route::get('/user', [
            'as' => 'user',
            'uses' => 'Admin\UserController@index'
        ]);

        Route::any('/user/update/{id?}', [
            'as' => 'user_update',
            'uses' => 'Admin\UserController@update'
        ]);

        Route::any('/user/edit/{id?}', [
            'as' => 'user_edit',
            'uses' => 'Admin\UserController@edit'
        ]);

        Route::any('/user/update', [
            'as' => 'user_update',
            'uses' => 'Admin\UserController@update'
        ]);

        Route::any('/user/status/update/{id?}', [
            'as' => 'user_status_update',
            'uses' => 'Admin\UserController@updateStatus'
        ]);

        Route::get('/user/list', [
            'as' => 'user_list',
            'uses' => 'Admin\UserController@list'
        ]);

        Route::get('/user/group/list', [
            'as' => 'user_group',
            'uses' => 'Admin\GroupController@index'
        ]);

        Route::any('/user/group/update/{id?}', [
            'as' => 'user_group_update',
            'uses' => 'Admin\GroupController@update'
        ]);

        Route::get('/user/group/delete/{id?}', [
            'as' => 'user_group_delete',
            'uses' => 'Admin\GroupController@destroy'
        ]);

        Route::any('/user/group/status/update/{id?}', [
            'as' => 'user_group_update_status',
            'uses' => 'Admin\GroupController@updateStatus'
        ]);

        Route::post('/user/group/adduser/{id?}', [
            'as' => 'user_group_add_user',
            'uses' => 'Admin\GroupController@addUserToGroup'
        ]);

        Route::post('/user/group/removeuser/{id?}', [
            'as' => 'user_group_remove_user',
            'uses' => 'Admin\GroupController@removeUserFromGroup'
        ]);

        Route::get('/user/permission', [
            'as' => 'user_permission',
            'uses' => 'Admin\PermissionController@index'
        ]);

        Route::post('/user/permission/update', [
            'as' => 'user_permission_update',
            'uses' => 'Admin\PermissionController@update'
        ]);

        Route::post('/user/permission/remove', [
            'as' => 'user_permission_remove',
            'uses' => 'Admin\PermissionController@remove'
        ]);
        /** ********************************* */

        /**
         * Category & News
         */
        Route::get('/news/category/list', [
            'as' => 'category',
            'uses' => 'Admin\CategoryController@index'
        ]);

        Route::get('/news/category/get/children', [
            'as' => 'category_get_children',
            'uses' => 'Admin\CategoryController@getChildren'
        ]);

        Route::get('/news/category/get/detail', [
            'as' => 'category_detail',
            'uses' => 'Admin\CategoryController@detail'
        ]);

        Route::post('/news/category/update', [
            'as' => 'category_update',
            'uses' => 'Admin\CategoryController@update'
        ]);

        Route::get('/news/list', [
            'as' => 'news',
            'uses' => 'Admin\NewsController@index'
        ]);

        Route::get('/news/edit/{id?}', [
            'as' => 'news_edit',
            'uses' => 'Admin\NewsController@edit'
        ]);

        Route::post('/news/update', [
            'as' => 'news_update',
            'uses' => 'Admin\NewsController@update'
        ]);

        Route::post('/news/update/by/field', [
            'as' => 'news_update_by_field',
            'uses' => 'Admin\NewsController@updateByField'
        ]);

        Route::post('/category/update/by/field', [
            'as' => 'category_update_by_field',
            'uses' => 'Admin\CategoryController@updateByField'
        ]);

        /*********************************************************/
        Route::any('/contact', [
            'as' => 'contact',
            'uses' => 'Admin\SettingController@contact'
        ]);
        Route::any('/setting', [
            'as' => 'setting',
            'uses' => 'Admin\SettingController@index'
        ]);
        Route::get('/help', [
            'as' => 'help',
            'uses' => 'Admin\SettingController@help'
        ]);
        //static_file
        Route::get('/static/page', [
            'as' => 'static_page',
            'uses' => 'Admin\SettingController@staticPage'
        ]);
        Route::any('/static/update/{id?}', [
            'as' => 'static_page_update',
            'uses' => 'Admin\SettingController@staticPageUpdate'
        ]);
        Route::post('/static/page/save/by/field', [
            'as' => 'static_page_update_by_field',
            'uses' => 'Admin\SettingController@staticPageSave'
        ]);
        Route::get('/static/page/delete/{file_name?}', [
            'as' => 'static_page_delete',
            'uses' => 'Admin\SettingController@staticFileDelete'
        ]);
        /*********************************************************/

        Route::get('/advertisement', [
            'as' => 'advertisement',
            'uses' => 'Admin\SettingController@advertisement'
        ]);

        Route::post('/advertisement/update', [
            'as' => 'advertisement_update',
            'uses' => 'Admin\SettingController@advertisementUpdate'
        ]);

        Route::post('/advertisement/update/status', [
            'as' => 'advertisement_update_status',
            'uses' => 'Admin\SettingController@advertisementUpdateStatus'
        ]);

        Route::get('/advertisement/delete/{id}', [
            'as' => 'advertisement_delete',
            'uses' => 'Admin\SettingController@advertisementDelete'
        ]);

    });

    //User current
    Route::group(['as' => 'session.', 'prefix' => 'person'], function () {


    });
});

Route::any('/ckfinder/connector', '\CKSource\CKFinderBridge\Controller\CKFinderController@requestAction')
    ->name('ckfinder_connector');

Route::any('/ckfinder/browser', '\CKSource\CKFinderBridge\Controller\CKFinderController@browserAction')
    ->name('ckfinder_browser');

Route::any('/ckfinder/select/files', '\CKSource\CKFinderBridge\Controller\CKFinderController@browserAction')
    ->name('ckfinder_select_files');

/*******************************FRONTEND*********************************/
Route::get('/', [
    'as' => 'home',
    'uses' => 'HomeController@index'
]);

Route::get('/{title}.html', [
    'as' => 'news_detail',
    'uses' => 'NewsController@detail'
]);

Route::get('/viewed/{news_id}', [
    'as' => 'news_viewed',
    'uses' => 'NewsController@viewed'
]);

Route::get('/{page_title}.shtml', [
    'as' => 'static_page',
    'uses' => 'HomeController@staticPage'
]);

Route::get('/logout', function() {
    \Illuminate\Support\Facades\Auth::logout();
    return redirect('/login');
});

Route::get('/{category_title}', [
    'as' => 'news',
    'uses' => 'NewsController@index'
]);