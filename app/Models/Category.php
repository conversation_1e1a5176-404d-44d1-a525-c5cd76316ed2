<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    protected $fillable = [
        'id', 'category_title', 'slugify', 'left_menu', 'category_description', 'category_level', 'category_title_seo', 'category_description_seo', 'category_keyword_seo', 'redirect_link', 'parent_id', 'status', 'hot', 'menu_top', 'photo', 'sort', 'created_at', 'updated_at'
    ];
}
