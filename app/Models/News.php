<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class News extends Model
{
    protected $fillable = [
        'id', 'category_id', 'title', 'slugify', 'viewed', 'title_seo', 'description_seo', 'keyword_seo', 'description', 'content', 'status', 'photo', 'date_public', 'date_end', 'author', 'video_url', 'source_url', 'tags', 'is_hot', 'is_focus', 'created_at', 'updated_at'
    ];
}
