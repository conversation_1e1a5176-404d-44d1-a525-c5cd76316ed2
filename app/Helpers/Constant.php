<?php

namespace App\Helpers;

use App\Models\Category;
use App\Models\News;

class Constant
{
    const IMAGE_SMALL_140 = 140;
    const IMAGE_SMALL_500 = 500;
    const IMAGE_SIZE_SLUGIGY = [
        140 => 't-',
        500 => 'x-'
    ];

    const IMAGE_SIZE_RESIZE = [
        140 => 't-',
        500 => 'x-'
    ];

    const ITEM_PER_PAGE = 50;
    const ON = "Bật";
    const OFF = "Tắt";

    const ACTIVE = "Mở";
    const INACTIVE = "Đóng";

    const YES = "Có";
    const NO = "Không";

    const ADMIN_INDEX = 2;
    const STAFF_INDEX = 1;
    const ADMIN = "Admin";
    const STAFF = "Biên tập viên";

    const WIDGET_TAI_SAO_SINH_VIEN_CHON_VUI = "TAI_SAO_SINH_VIEN_CHON_VUI";
    const WIDGET_NGANH_DAO_TAO = "WIDGET_NGANH_DAO_TAO";
    const WIDGET_WEB_LINK = "WEB_LINK";
    const WIDGET_STATIC_FILE = "STATIC_FILE";

    const MAIL_SETTING_KEY = "MAIL_SETTING";
    const CONTACT_SETTING_KEY = "CONTACT_SETTING";
    const WIDGET_SLIDE = "SLIDES";

    const CATEGORY_ROUTER = 'admin.category';
    const CATEGORY_DOCUMENT_ROUTER = 'admin.category_document';

    const STATUS_YES_NO_INT = [
        "YES" => 1,
        "NO" => 0,
    ];

    const STATUS_YES_NO_LABEL = [
        1 => self::YES,
        0 => self::NO,
    ];

    const STATUS_ON_OFF_LABEL = [
        1 => self::ON,
        0 => self::OFF,
    ];

    const STATUS_USER_ROLES_LABEL = [
        self::STAFF_INDEX => self::STAFF,
        self::ADMIN_INDEX => self::ADMIN,
    ];

    const PERMISSION_USER_LIST = 'PERMISSION_USER_LIST';                               // Danh sach nguoi dung
    const PERMISSION_USER_UPDATE = 'USER_UPDATE';                           // Cap nhat nguoi dung
    const PERMISSION_CATEGORY = 'PERMISSION_CATEGORY';                      // Danh muc Tin tuc
    const PERMISSION_NEWS = 'PERMISSION_NEWS';                              // Tin tuc
    const PERMISSION_STATIC_PAGE = 'PERMISSION_STATIC_PAGE';
    const PERMISSION_SETTING = 'PERMISSION_SETTING';

    const PERMISSION_DATA = [
        self::PERMISSION_USER_LIST => [
            'title' => 'Danh sách người dùng',
        ],
        self::PERMISSION_USER_UPDATE => [
            'title' => 'Cập nhật người dùng',
        ],
        self::PERMISSION_CATEGORY => [
            'title' => 'Danh mục bài viết',
        ],
        self::PERMISSION_NEWS => [
            'title' => 'Tin tức Bài viết',
        ],
        self::PERMISSION_STATIC_PAGE => [
            'title' => 'Cập nhật Trang tĩnh',
        ],
        self::PERMISSION_SETTING => [
            'title' => 'Cấu hình Website',
        ],
    ];

    const LEFT_HEADERS = [
        0 => "Không hiển thị",
        1 => "Tin tức",
        2 => "Ban ngành trực thuộc",
    ];

    const ADVERTISEMENTS = [
        1 => 'Bên Phải',
        2 => 'Bên Trái',
        //3 => 'Giữa Trang chủ',
    ];

    const ADVERTISEMENTS_RIGHT = 1;
    const ADVERTISEMENTS_LEFT = 2;
    const ADVERTISEMENTS_HOME = 3;


    public static function routerLeftHeader()
    {
        $news = Category::whereIn('id', [26,191])->get();
        $routers = [];
        foreach ($news as $row) {
            $routers[$row->id == 26 ? 1 : 2] = route('news', ['category_title' => Utils::slugify($row->category_title)]);
        }
        return $routers;
    }

    public static function optionAdvertisement($selected = null)
    {
        $htmlOption = "";
        foreach (self::ADVERTISEMENTS as $val => $label) {
            $optionSelected = $selected === $val ? 'selected' : '';
            $htmlOption .= "<option value='$val' $optionSelected>$label</option>";
        }
        return $htmlOption;
    }

    public static function optionUserRole($selected = null)
    {
        $htmlOption = "";
        foreach (self::STATUS_USER_ROLES_LABEL as $val => $label) {
            $optionSelected = $selected === $val ? 'selected' : '';
            $htmlOption .= "<option value='$val' $optionSelected>$label</option>";
        }
        return $htmlOption;
    }

    public static function optionLeftMenu($selected = 0)
    {
        $htmlOption = "";
        foreach (self::LEFT_HEADERS as $val => $label) {
            $optionSelected = $selected === $val ? 'selected' : '';
            $htmlOption .= "<option value='$val' $optionSelected>$label</option>";
        }
        return $htmlOption;
    }

    public static function optionOnOff($selected = 0)
    {
        $htmlOption = "";
        foreach (self::STATUS_ON_OFF_LABEL as $val => $label) {
            $optionSelected = $selected === $val ? 'selected' : '';
            $htmlOption .= "<option value='$val' $optionSelected>$label</option>";
        }
        return $htmlOption;
    }

    public static function optionYesNo($selected = 0)
    {
        $htmlOption = "";
        foreach (self::STATUS_YES_NO_LABEL as $val => $label) {
            $optionSelected = $selected === $val ? 'selected' : '';
            $htmlOption .= "<option value='$val' $optionSelected>$label</option>";
        }
        return $htmlOption;
    }

    public static function optionCategories($categories, $idSelected = "")
    {
        $html = "";
        if ($categories) {
            foreach ($categories as $category) {
                $selected = $idSelected == $category->id ? 'selected' : '';
                $html .= "<option $selected value = '{$category->id}'>" . ($category->category_title) . "</option>";

                if (!empty($category->children)) {
                    foreach ($category->children as $category2) {
                        $selected = $idSelected == $category2->id ? 'selected' : '';
                        $html .= "<option $selected value='{$category2->id}'>-- " . ($category2->category_title) . "</option >";
                        if (!empty($category2->children)) {
                            foreach ($category2->children as $category3) {
                                $selected = $idSelected == $category3->id ? 'selected' : '';
                                $html .= "<option $selected value='{$category3->id}'>--- " . ($category3->category_title) . "</option >";
                            }
                        }
                    }
                }
            }
        }
        return $html;
    }
}
