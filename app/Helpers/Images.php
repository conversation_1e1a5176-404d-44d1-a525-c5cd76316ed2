<?php


namespace App\Helpers;


use App\Models\Advertisement;
use Intervention\Image\Facades\Image;

class Images
{
    protected static $destination = "uploads/";

    /**
     * @param $file: '/uploads/images/....'
     * @param int $width
     * @param int $height
     * @param string $destination
     */
    public static function resize($file, $width = 140, $height = 100, $destination = '')
    {
        try {
            if(empty(Constant::IMAGE_SIZE_RESIZE[$width])) {
                return $file;
            }

            $file = urldecode($file);

            $filename = basename($file);
            $pathFile = public_path(substr($file, 1));
            if(!file_exists($pathFile)) {
                return '/images/news_default.png';
            }

            $newPath = str_replace($filename, Constant::IMAGE_SIZE_RESIZE[$width] . $filename, $file);

            $imageObj = Image::make($pathFile);
            $widthOrigin = $imageObj->width();

            if($widthOrigin <= $width) {
                return $file;
            }

            if(!$destination) {
                $destination = str_replace($filename, Constant::IMAGE_SIZE_RESIZE[$width] . $filename, $pathFile);
            }

            if(file_exists($destination)) {
                return $newPath;
            }

            $imageObj->resize($width, $height);
            $imageObj->save($destination);

            return $newPath;
        } catch (\Exception $e) {
            $pathFile = public_path(substr($file, 1));
            if(file_exists($pathFile)) {
                return $file;
            }
            return '/images/news_default.png';
        }
    }

    public static function getAdvertisement($position = 1)
    {
        $data = Advertisement::where('status', 1)->where('position', $position)->orderBy('sort', 'ASC')->get();
        return $data;
    }
}