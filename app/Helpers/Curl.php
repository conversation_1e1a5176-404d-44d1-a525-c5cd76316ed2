<?php

namespace App\Helpers;

class Curl
{
    public $ch = null;
    public $options = array();
    public $cookiePath = ('F:\Works\webs\pgthanhhoa\storage\app\pgth_cookie.txt');

    function __construct()
    {
        $this->ch = curl_init();
        $this->options = [
            CURLOPT_HEADER => FALSE,
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_COOKIEFILE => $this->cookiePath,
            //CURLOPT_USERAGENT => $_SERVER['HTTP_USER_AGENT'],
            CURLOPT_FOLLOWLOCATION=>true,
            //CURLOPT_COOKIEJAR => $this->cookiePath
        ];
    }

    function setUrlAndMethod($url, $method)
    {
        $this->options[CURLOPT_URL] = $url;
        $this->options[CURLOPT_POST] = $method == 'POST';
        if($method == 'GET') {
            $this->options[CURLOPT_POSTFIELDS] = null;
        }
    }

    function optOutBazaarVoice($url, $method)
    {
        try {
            $this->setUrlAndMethod($url, $method);
            curl_setopt_array($this->ch, $this->options);
            $result = curl_exec($this->ch);
            return $result;
        } catch (\Exception $exception) {
            return null;
        }
    }

    function home($url, $method)
    {
        try {
            $this->setUrlAndMethod($url, $method);
            curl_setopt_array($this->ch, $this->options);
            $result = curl_exec($this->ch);
            return $result;
        } catch (\Exception $exception) {
            return null;
        }
    }

    function login($url, $method, $params)
    {
        try {
            $this->setUrlAndMethod($url, $method);
            $this->options[CURLOPT_POSTFIELDS] = $params;
            curl_setopt_array($this->ch, $this->options);
            $result = curl_exec($this->ch);
            return $result;
        } catch (\Exception $exception) {
            return null;
        }
    }

    function category($url, $method, $params)
    {
        try {
            $this->setUrlAndMethod($url, $method);
            $this->options[CURLOPT_POSTFIELDS] = $params;
            curl_setopt_array($this->ch, $this->options);
            $result = curl_exec($this->ch);
            return $result;
        } catch (\Exception $exception) {
            return null;
        }
    }

    function newsNextPage($url, $method, $params)
    {
        try {
            $this->setUrlAndMethod($url, $method);
            $this->options[CURLOPT_POSTFIELDS] = $params;
            curl_setopt_array($this->ch, $this->options);
            $result = curl_exec($this->ch);
            return $result;
        } catch (\Exception $exception) {
            return null;
        }
    }

    function news($url, $method)
    {
        try {
            $this->setUrlAndMethod($url, $method);
            curl_setopt_array($this->ch, $this->options);
            $result = curl_exec($this->ch);
            return $result;
        } catch (\Exception $exception) {
            return null;
        }
    }

    function newsDetail($id, $url, $method)
    {
        try {
            $this->setUrlAndMethod($url, $method);
            curl_setopt_array($this->ch, $this->options);
            $result = curl_exec($this->ch);
            return $result;
        } catch (\Exception $exception) {
            return null;
        }
    }

    function page($url, $method)
    {
        try {
            $this->setUrlAndMethod($url, $method);
            curl_setopt_array($this->ch, $this->options);
            $result = curl_exec($this->ch);
            return $result;
        } catch (\Exception $exception) {
            return null;
        }
    }

    function logout($url, $method, $params)
    {
        try {
            $this->setUrlAndMethod($url, $method);
            $this->options[CURLOPT_POSTFIELDS] = $params;
            curl_setopt_array($this->ch, $this->options);
            $result = curl_exec($this->ch);
            return $result;
        } catch (\Exception $exception) {
            return null;
        }
    }

    function __destruct()
    {
        $this->close();
    }

    function close()
    {
        curl_close($this->ch);
    }
}

