<?php


namespace App\Helpers;


use PHPHtmlParser\Dom;

class RemoteV4U
{
    protected $u = 'transon';
    protected $p = 'son1762841593';
    protected $tk = '';
    protected $rt = '';
    protected $newsPage = 157;
    protected $categoryPage = 6;

    protected $curl = null;
    protected $urls = [
        "home" => [
            "url" => "https://groot.phatgiaothanhhoa.com/",
            "method" => 'GET'
        ],
        "pre_login" => [
            "url" => "https://groot.phatgiaothanhhoa.com/",
            "method" => 'GET'
        ],
        "login" => [
            "url" => "https://groot.phatgiaothanhhoa.com/task/",
            "params" => [
                "rt" => "login",
                "u" => "",
                "p" => "",
                "tk" => ""
            ],
            "method" => 'POST'
        ],

        "category" => [
            "url" => "https://groot.phatgiaothanhhoa.com/menu/tak",
            "data" => [

            ],
            "method" => 'POST'
        ],
        "news" => [
            "url" => "https://groot.phatgiaothanhhoa.com/news",
            "data" => [

            ],
            "method" => 'GET'
        ],
        "news_next_page" => [
            "url" => "https://groot.phatgiaothanhhoa.com/news/tak",
            "data" => [

            ],
            "method" => 'POST'
        ],
        "news_edit" => [
            "url" => "https://groot.phatgiaothanhhoa.com/news/edit/{news_id}",
            "data" => [

            ],
            "method" => 'GET'
        ],

        "page" => [
            "url" => "https://groot.phatgiaothanhhoa.com/page",
            "data" => [

            ],
            "method" => 'GET'
        ],
        "logout" => [
            "url" => "https://groot.phatgiaothanhhoa.com/task",
            "params" => [
                "rt" => "logout",
                "tk" => ""
            ],
            "method" => 'POST'
        ],
    ];

    public function __construct()
    {
        $this->curl = new Curl();
    }

    function optOutBazaarVoice($url, $method)
    {
        $data = $this->curl->optOutBazaarVoice($url, $method);
        return $data;
    }

    function home()
    {
        $data = $this->curl->home($this->urls['home']['url'], $this->urls['home']['method']);
        if($data && strpos($data, '>Thoát<') !== false) {
            preg_match("/tk:'([^']+)'/", $data,$match);
            if(!empty($match[1])) {
                return $match[1];
            }
        }
        return false;
    }

    function preLogin()
    {
        $data = $this->curl->home($this->urls['pre_login']['url'], $this->urls['pre_login']['method']);
        preg_match("/tk:'([^']+)'/", $data,$match);
        if(!empty($match[1])) {
            return $match[1];
        }
        return false;
    }

    function login($tk)
    {
        $params['rt'] = 'login';
        $params['tk'] = $tk;
        $params['u'] = md5($this->u);
        $params['p'] = md5($this->p);
        $data = $this->curl->login($this->urls['login']['url'], $this->urls['login']['method'], $params);

        if($data && strpos($data, 'true') !== false) {
            return true;
        }
        return false;
    }

    function category($tk)
    {
        for($i = 1; $i <= $this->categoryPage; $i++) {
            $params = [
                'rt' => 'nextPage',
                'rtc' => '',
                'tk' => $tk,
                'p' => $i . 's',
            ];
            $data = $this->curl->category($this->urls['category']['url'], $this->urls['category']['method'], $params);
            if ($data) {
                $dom = new Dom;
                $dom->loadStr($data);
                Utils::processCategoryData($dom);
            }
        }
        return false;
    }

    function news()
    {
        $data = $this->curl->news($this->urls['news']['url'], $this->urls['news']['method']);
        if($data) {
            $dom = new Dom();
            $dom->loadStr($data);
            $nIDs = Utils::processNewsData($dom);
            if($nIDs) {
                file_put_contents(storage_path('app/news_id.txt'), implode("\n", $nIDs));
            }
            return $data;
        }
        return false;
    }

    function newsNextPage($tk)
    {
        for($i = 1; $i <= $this->newsPage; $i++) {
            $params = [
                'rt' => 'nextPage',
                'rtc' => '',
                'tk' => $tk,
                'p' => $i . 's',
            ];
            $data = $this->curl->newsNextPage($this->urls['news_next_page']['url'], $this->urls['news_next_page']['method'], $params);
            if ($data) {
                $dom = new Dom();
                $dom->loadStr($data);
                $nIDs = Utils::processImageNewsData($dom);
                if ($nIDs) {
                    file_put_contents(storage_path('app/news_id_img.txt'), "\n" . implode("\n", $nIDs), FILE_APPEND);
                }
            }
            echo $i . ' - ';
            if($i % 10 == 0) {
                sleep(1);
            }
        }
        return false;
    }

    function newsNextPageGetView($tk)
    {
        for($i = 1; $i <= $this->newsPage; $i++) {
            $params = [
                'rt' => 'nextPage',
                'rtc' => '',
                'tk' => $tk,
                'p' => $i . 's',
            ];
            $data = $this->curl->newsNextPage($this->urls['news_next_page']['url'], $this->urls['news_next_page']['method'], $params);
            if ($data) {
                $dom = new Dom();
                $dom->loadStr($data);
                $nIDs = Utils::processViewNewsData($dom);
                if ($nIDs) {
                    file_put_contents(storage_path('app/news_id_viewed.txt'), "\n" . implode("\n", $nIDs), FILE_APPEND);
                }
            }
            echo $i . ' - ';
            if($i % 10 == 0) {
                sleep(1);
            }
        }
        return false;
    }

    function newsDetail($id, $url)
    {
        $data = $this->curl->newsDetail($id, $url, 'GET');
        if($data) {
            $dom = new Dom;
            $dom->loadStr($data);

            // get container html
            $containerHtml = $dom->find('#myBody');
            Utils::processNewsDetailData($id, $containerHtml);
        }
    }

    function page()
    {
        $data = $this->curl->page($this->urls['page']['url'], $this->urls['page']['method']);
        if($data) {
            $dom = new Dom();
            $dom->loadStr($data);
            $buyercode = $dom->findById('buyercode');
        }
        return false;
    }

    function logout($params)
    {
        $data = $this->curl->logout($this->urls['logout']['url'], $this->urls['logout']['method'], $params);
        return $data;
    }

    public function __destruct()
    {
        // TODO: Implement __destruct() method.
    }
}