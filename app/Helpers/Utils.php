<?php
namespace App\Helpers;

use App\Models\Category;
use App\Models\District;
use App\Models\News;
use App\Models\PaymentTransaction;
use App\Models\Setting;
use App\Models\Viewed;
use App\Repositories\Setting\SettingRepository;
use App\Repositories\Transaction\TransactionRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use PHPHtmlParser\Dom;

class Utils {
    public static function noteFormat($note, $user = [], $append = '')
    {
        if(!$user) {
            $user = Auth::user();
        }
        $class = $user->is_role > 0 ? 'amt-admin' : 'amt-user';
        return "<p><strong class='$class'>".date("h:m d/m/Y")." ".$user->name.":</strong> $note</p>" . $append;
    }


    public static function getRemoteSession($request)
    {
        $remoteSession = $request->session()->get(Constant::REMOTE_SESSION);
        return $remoteSession;
    }

    public static function setRemoteSession($user, $username, $password)
    {
        session([Constant::REMOTE_SESSION => [
            'user_id' => $user->id,
            'username' => $username,
            'password' => $password,
            'time' => time(),
        ]]);
    }

    public static function setRemoteTimeSession($user, $request)
    {
        $remoteSession = self::getRemoteSession($request);
        if($remoteSession) {
            self::setRemoteSession($user, $remoteSession['username'], $remoteSession['password']);
        }
    }

    /**
     * @param $remoteV4U = new RemoteU4v()
     * @param $user
     * @param $request
     * @return bool
     */
    public static function checkRemoteSession($remoteV4U, $user, $request)
    {
        $homeRemote = $remoteV4U->home();
        if($homeRemote) {
            Utils::setRemoteTimeSession($user, $request);
            return true;
        } else {
            $request->session()->forget(Constant::REMOTE_SESSION);
            return false;
        }
    }

    public static function sTrimData($data, $search = ['$','&nbsp;'], $replace = '')
    {
        return trim(str_replace($search, $replace, $data));
    }

    /**
     * @param string $currentTimezone
     * @param string $toTimeZone
     * @param $date
     * @param string $format
     * @return false|string
     */
    public static function convertDateTimeByZone($date, $currentTimezone = 'UTC', $toTimeZone = 'Asia/Ho_Chi_Minh', $format = "Y-m-d H:i:s")
    {
        date_default_timezone_set($currentTimezone);
        $timestamp = strtotime($date);

        date_default_timezone_set($toTimeZone);
        $toDatetime = date($format, $timestamp);
        return $toDatetime;
    }

    /**
     * @param $id
     * @param $dom
     * @return array
     */
    public static function processNewsDetailData($id, $dom)
    {
        try {
            $titleSeo = $dom->find('#tts');
            $title = $dom->find('#tt');

            $myimage = "";
            try {
                $myimage = $dom->find('#myimage')[0]->find('img')->getAttribute('src');
            } catch (\Exception $e) {

            }
            $description = $dom->find('#tempDes');
            $category = $dom->find('#mainMenu')[0]->find('select');
            $urlVideo = $dom->find('#uvideo');
            $author = $dom->find('#author');
            $source = $dom->find('#source');
            //$hpublic = $dom->find('#hpublic');
            //$ipublic = $dom->find('#ipublic');
            $dpublic = $dom->find('#dpublic');
            $contags = $dom->find('#contags');
            $tempHtml = $dom->find('#tempHtml');

            $category_id = 0;
            if($category) {
                $categories = $category->find('option');
                foreach ($categories as $c) {
                    if($c->getAttribute('selected') == 'selected') {
                        $category_id = $c->getAttribute('value');
                        break;
                    }
                }
            }

            $hour = 12;
            /*if($hpublic) {
                $hpublics = $hpublic->find('option');
                foreach ($hpublics as $h) {
                    $selected = $h->getAttribute('selected');
                    if($selected == 'selected') {
                        $hour = $h->getAttribute('value');
                        break;
                    }
                }
            }*/
            $minute = 0;
            /*if($ipublic) {
                $ipublics = $ipublic->find('option');
                foreach ($ipublics as $i) {
                    $selected = $i->getAttribute('selected');
                    if($selected == 'selected') {
                        $minute = $i->getAttribute('value');
                        break;
                    }
                }
            }*/

            $date_public = "";
            if($dpublic->getAttribute('value')) {
                $time = (str_replace('/', '-', $dpublic->getAttribute('value')) . " $hour:$minute:00");
                $date_public = date("Y-m-d H:i:s", strtotime($time));
            }

            $tags = [];
            if($contags) {
                $contags = $contags->find('.btn');
                foreach ($contags as $a) {
                    $tags[] = $a->find('span')[0]->text();
                }
            }

            $data = [
                'id' => $id,
                'category_id' => $category_id,
                'title' => $title->getAttribute('value'),
                'title_seo' => $titleSeo->getAttribute('value'),
                'description_seo' => $titleSeo->getAttribute('value'),
                'keyword_seo' => '',
                'description' => $description->text(),
                'content' => $tempHtml->innerHtml(),
                'status' => 1,
                'author' => $author->text(),
                'video_url' => $urlVideo->text(),
                'source_url' => $source->text(),
                'tags' => json_encode($tags),
                'photo' => $myimage,
            ];

            if($date_public) {
                $data['date_public'] = $date_public;
            }

            News::create($data);

        } catch (\Exception $e) {
            print_r($e);
            die;
        }
    }

    public static function processImageNewsData($dom)
    {
        try {
            $results = [];
            $rows = $dom->find('.remove');
            foreach ($rows as $row) {
                // get the class attr
                $class = $row->getAttribute('class');
                $id = str_replace("rm", "", explode(" ", $class)[1]);

                //get img
                $img = $row->find('img');
                if($img) {
                    $img = $img[0];
                }

                $results[] = $id ."||".$img->getAttribute('src');
            }

            return $results;
        } catch (\Exception $e) {
            return [];
        }
    }

    public static function processViewNewsData($dom)
    {
        try {
            $results = [];
            $rows = $dom->find('.remove');
            foreach ($rows as $row) {
                // get the class attr
                $class = $row->getAttribute('class');
                $id = str_replace("rm", "", explode(" ", $class)[1]);

                //get img
                $img = $row->find('.mb-2').find('.small');
                if($img) {
                    $img = $img[0];
                }

                $results[] = $id ."||".$img->getAttribute('src');
            }

            return $results;
        } catch (\Exception $e) {
            return [];
        }
    }

    public static function processNewsData($dom)
    {
        try {
            $results = [];
            $rows = $dom->find('.remove');
            foreach ($rows as $row) {
                // get the class attr
                $class = $row->getAttribute('class');
                $id = str_replace("rm", "", explode(" ", $class)[1]);
                $results[] = $id;
            }

            return $results;
        } catch (\Exception $e) {
            return [];
        }
    }

    public static function processCategoryData($dom)
    {
        /** @var $dom Dom */
        try {
            $rows = $dom->find('form');
            foreach ($rows as $row) {

                // get the class attr
                //$id = $row->find('input[name=id]')->getAttribute('value');
                $array = [];
                $inputs = $row->find('input');
                foreach ($inputs as $input) {
                    $name = $input->getAttribute('name');
                    if(!$name) {
                        $placeholder = $input->getAttribute('placeholder');
                        if($placeholder == 'Tên danh mục!') {
                            $name = 'tt';
                        }
                    }
                    switch ($name) {
                        case 'id':
                            $id = $input->getAttribute('value');
                            $array['id'] = $id;
                            break;

                        case 'tt':
                            $title = $input->getAttribute('value');
                            $array['category_title'] = $title;
                            break;

                        case 'tts':
                            $value = $input->getAttribute('value');
                            $array['category_title_seo'] = $value;
                            break;

                        case 'key':
                            $value = $input->getAttribute('value');
                            $array['category_keyword_seo'] = $value;
                            break;

                        case 'img':
                            $value = $input->getAttribute('value');
                            $array['photo'] = $value;
                            break;

                        case 'hot':
                            $value = $input->getAttribute('value');
                            $array['hot'] = $value;
                            break;

                        case 'des':
                            $value = $input->getAttribute('value');
                            $array['category_description_seo'] = $value;
                            break;
                    }
                }

                $parent = $row->find('select[name=parent]');
                $parents = $parent->find('option');
                $parent_id = 0;
                foreach ($parents as $p) {
                    $html = $p->outerHtml();
                    if(strpos($html, 'selected') !== false) {
                        $parent_id = $p->getAttribute('value');
                        break;
                    }
                }
                $array['parent_id'] = $parent_id;

                Category::create($array);
            }
        } catch (\Exception $e) {
            print_r($e);die;
        }
    }

    public static function downloadImageFromUrl($localPath, $url)
    {

        $content = self::file_get_contents_curl($url);

        if(strpos($content, '404 Not Found')) {
            $content = self::file_get_contents_curl(str_replace(['/s-'], '', $url));
        }

        file_put_contents($localPath, $content);
        //Storage::disk('local')->put($localPath, self::file_get_contents_curl($url));
    }

    public static function file_get_contents_curl($url) {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_URL, $url);

        $data = curl_exec($ch);
        curl_close($ch);

        return $data;
    }

    public static function extraImageUrls($html)
    {
        $images = [];
        preg_match_all('/<img.*?src=[\'"](.*?)[\'"].*?>/i', $html, $matches);

        if($matches && count($matches) > 1 && !empty($matches[1])) {
            $urls = $matches[1];
            foreach ($urls as $url) {
                if (filter_var($url, FILTER_VALIDATE_URL)) {
                    $images[] = $url;
                }
            }
        }
        return $images;
    }

    public static function formatCategories($categories, $parentId = 0)
    {
        return self::buildTree($categories, $parentId);
    }

    public static function mapsCategory($categories, $field = 'id', $fieldValue = null)
    {
        $results = [];
        if($categories) {
            foreach ($categories as $category) {
                if(isset($category->{$field})) {
                    $results[$category->{$field}] = $fieldValue && isset($category->{$fieldValue}) ? (object)[$fieldValue => $category->{$fieldValue}] : $category;
                }
            }
        }

        return $results;
    }

    /**
     * @param $data
     * @param string $langCode
     */
    public static function getContentByLangCode($data = "", $langCode = 'vn')
    {
        try {
            if(!$data) {
                return "";
            }
            if(is_array($data)) {
                return !empty($data[$langCode]) ? $data[$langCode] : null;
            }
            $arrayParse = (array)json_decode($data);
            return is_array($arrayParse) && !empty($arrayParse[$langCode]) ? $arrayParse[$langCode] : null;
        } catch (\Exception $e) {
            return null;
        }
    }

    public static function checkDuplicate($excelData, $order_type)
    {
        $response = ['message' => 'OK', 'status' => 0];
        if ($excelData && !empty($excelData[0])) {
            $orders = $excelData[0];
            $lists = [];
            foreach ($orders as $idx => $order) {
                if($idx > 0 && !empty($order[0])) {
                    //$lists[] = $order[0];
                    if ($order_type == Constant::ONLINE) {
                        $identity = $order[0];
                        $lists[] = $identity;
                    } elseif ($order_type == Constant::OFFLINE) {
                        $memberCode = $order[0];
                        $lists[] = $memberCode;
                    }
                }
            }
            if($lists) {
                $withoutDuplicates = array_unique($lists);
                $duplicates = array_unique(array_diff_assoc($lists, $withoutDuplicates));
                if($duplicates) {
                    $response = ['message' => 'Duplicated data, please check the Excel file again: '. implode('; ', array_unique($duplicates)), 'status' => 1];
                }
            }
        }
        return ($response);
    }

    public static function formatDateToCalendar($date = "")
    {
        return !$date ? '' : date('d/m/Y', strtotime($date));
    }

    public static function checkPermission($key, $user)
    {
        if($user->is_role == 2) {
            return true;
        }

        $userPermissions = $user->permissions ? json_decode($user->permissions) : [];
        return $userPermissions ? in_array($key, $userPermissions) : false;
    }

    function readableBytes($bytes)
    {
        $i = floor(log($bytes) / log(1024));
        $sizes = array('B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB');

        return sprintf('%.02F', $bytes / pow(1024, $i)) * 1 . ' ' . $sizes[$i];
    }

    public static function readableBytesToMb($bytes)
    {
        return round($bytes / pow(1024, 2), 2) * 1;
    }

    public static function vietnamese_to_latin($text)
    {
        $text = preg_replace("/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/", "a", $text);
        $text = preg_replace("/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/", "A", $text);
        $text = preg_replace("/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/", "o", $text);
        $text = preg_replace("/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/", "O", $text);
        $text = preg_replace("/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/", "u", $text);
        $text = preg_replace("/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/", "U", $text);
        $text = preg_replace("/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/", "e", $text);
        $text = preg_replace("/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/", "E", $text);
        $text = preg_replace("/ì|í|ị|ỉ|ĩ/", "i", $text);
        $text = preg_replace("/Ì|Í|Ị|Ỉ|Ĩ/", "I", $text);
        $text = preg_replace("/ỳ|ý|ỵ|ỷ|ỹ/", "y", $text);
        $text = preg_replace("/Ỳ|Ý|Ỵ|Ỷ|Ỹ/", "Y", $text);
        $text = preg_replace("/đ/", "d", $text);
        $text = preg_replace("/Đ/", "D", $text);
        return $text;
    }

    public static function slugify($text, $separator = '-', $lower = true)
    {
        $text = self::vietnamese_to_latin($text);
        $text = preg_replace('~[^\\pL\d]+~u', $separator, $text);
        $text = trim($text, $separator);
        if (function_exists('iconv')) {
            $text = iconv('utf-8', 'ISO-8859-1//TRANSLIT//IGNORE', $text);
        }
        if ($lower)
            $text = strtolower($text);
        $text = preg_replace('~[^-\w]+~', '', $text);
        if (empty($text)) {
            return '-';
        }

        return rawurlencode($text);
    }

    public static function formatPathFile($path)
    {
        if(!$path) {
            return $path;
        }
        return $path[0] != '/' ? "/$path" : $path;
    }

    public static function buildTree($elements, $parentId = 0) {
        $branch = array();

        foreach ($elements as $element) {
            if ($element->parent_id == $parentId || $element->parent_id == $element->id) {
                $children = self::buildTree($elements, $element->id);
                if ($children) {
                    $element->children = $children;
                }
                $branch[] = $element;
            }
        }

        return $branch;
    }

    public static function buildTreeWithArray($elements, $parentId = 0) {
        $branch = array();

        foreach ($elements as $element) {
            if ($element['parent_id'] == $parentId) {
                $children = self::buildTreeWithArray($elements, $element['id']);
                if ($children) {
                    $element['children'] = $children;
                }
                $branch[] = $element;
            }
        }

        return $branch;
    }

    public static function getSetting($key = null)
    {
        if(!$key) {
            return $key;
        }
        $setting = Setting::where('key', $key)->first();
        if($setting) {
            return $setting->values;
        }
        return null;
    }

    public static function convertYoutubeToEmbed($string) {
        return preg_replace(
            "/\s*[a-zA-Z\/\/:\.]*youtu(be.com\/watch\?v=|.be\/)([a-zA-Z0-9\-_]+)([a-zA-Z0-9\/\*\-\_\?\&\;\%\=\.]*)/i",
            "<iframe width='100%' height='250px' src=\"//www.youtube.com/embed/$2\" allowfullscreen></iframe>",
            $string
        );
    }

    public static function truncateStringByWord($string, $len = 80)
    {
        return strtok(wordwrap(strip_tags($string), $len, "...\n"), "\n");
    }

    public static function viewed($request, $news)
    {
        $viewed = Viewed::where('news_id', $news->id)->whereDate('created_at', date('Y-m-d'))->first();
        if(!empty($viewed)) {
            $viewed->counter = $viewed->counter + 1;
            $viewed->save();

            $total_counter = Viewed::where('news_id', $news->id)->select(DB::raw('SUM(counter) as total_counter'))->first();
            $news->viewed = $news->viewed + $total_counter->total_counter;
        } else {
            Viewed::create(['news_id' => $news->id, 'counter' => 1]);
            $news->viewed = $news->viewed + 1;
        }
        $news->save();
    }
    
    public static function getRangThisWeek()
    {
        $monday = strtotime("last monday");
        $monday = date('w', $monday) == date('w') ? $monday+7*86400 : $monday;
        $sunday = strtotime(date("Y-m-d",$monday)." +6 days"); //+6 can be changed to +1,+2...so on acc to your need
        $this_week_sd = date("Y-m-d",$monday);
        $this_week_ed = date("Y-m-d",$sunday);
        
        return [
          'start_time' => $this_week_sd . ' 00:00:00',
          'end_time' => $this_week_ed . ' 23:59:59',
        ];
    }
}
