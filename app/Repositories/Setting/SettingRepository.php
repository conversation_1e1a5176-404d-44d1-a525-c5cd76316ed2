<?php

namespace App\Repositories\Setting;
use App\Helpers\Constant;
use App\Models\Setting;
use App\Repositories\EloquentRepository;

class SettingRepository extends EloquentRepository implements SettingRepositoryInterface
{
    /**
     * get model
     * @return string
     */
    public function getModel()
    {
        return \App\Models\Setting::class;
    }

    public function saveData($key, $values)
    {
        $setting = Setting::where('key', $key)->first();
        if($setting) {
            return $this->update($setting->id, [
                'values' => ($values)
            ]);
        } else {
            return $this->create([
                'key' => $key,
                'values' => ($values)
            ]);
        }
    }

    public function saveDataJSON($attributes)
    {
        $key = $attributes['key'];
        $setting = $this->findFirstWhere(['key' => $key]);
        if($setting) {
            return $this->update($setting->id, [
                'values' => json_encode($attributes['values'])
            ]);
        } else {
            return $this->create([
                'key' => $key,
                'values' => json_encode($attributes['values'])
            ]);
        }
    }

    public function getData($key)
    {
        $setting = $this->findFirstWhere(['key' => $key]);
        if($setting) {
            $setting->values = json_decode($setting->values);
        }
        return $setting;
    }
}
