<?php

namespace App\Repositories\Category;
use App\Helpers\Utils;
use App\Repositories\EloquentRepository;

class CategoryRepository extends EloquentRepository implements CategoryRepositoryInterface
{
    public $code;
    /**
     * get model
     * @return string
     */
    public function getModel()
    {
        return \App\Models\Category::class;
    }

    public function getCategoriesByParentId($parent_id = 0)
    {
        try {
            return $this->_model->where('parent_id', $parent_id)->orderBy('id', 'DESC')->get();
        } catch (\Exception $e) {

        }
    }

    public function getCategoriesLevel12()
    {
        try {
            return $this->_model->where('category_level', '<=', 2)->orderBy('id', 'ASC')->get();
        } catch (\Exception $e) {

        }
    }

    public function getLeftCategories()
    {
        $categories = $this->_model->where('left_menu', '>=', 1)->orderBy('id', 'DESC')->get();
        return $categories;
    }

    public function getCategoriesActive()
    {
        return $this->_model->where('status', 1)->orderBy('sort')->get();
    }

    public function saveCategory($data, $request)
    {
        try {
            if(empty($data['id'])) {
                return $this->create($data);
            } else {
                return $this->update($data['id'], $data);
            }
        } catch (\Exception $e) {
            return null;
        }
    }

    public function getCategory($id, $request)
    {
        // TODO: Implement getCategory() method.
    }

    public function removeCategory($id, $request)
    {
        $check = $this->findFirstWhere(['user_id' => $id, 'path' => $request->input('path')]);
        if($check) {
            return $check->delete();
        }
        return false;
    }

    public function getHotCategories()
    {
        try {
            return $this->_model->where('status', 1)->where('hot', 1)->orderBy('id', 'DESC')->get();
        } catch (\Exception $e) {

        }
    }
}
