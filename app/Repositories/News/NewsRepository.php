<?php

namespace App\Repositories\News;
use App\Helpers\Constant;
use App\Helpers\Utils;
use App\Models\News;
use App\Models\Tag;
use App\Repositories\EloquentRepository;

class NewsRepository extends EloquentRepository implements NewsRepositoryInterface
{
    public $code;
    /**
     * get model
     * @return string
     */
    public function getModel()
    {
        return \App\Models\News::class;
    }

    public function initNewsORM()
    {
        return News::where('status', 1)->whereRaw('created_at >= IFNULL(date_public, "2001-01-01") AND created_at <= IFNULL(date_end, NOW())');
    }

    public function saveNews($data, $request)
    {
        try {
            if(empty($data['id'])) {
                return $this->create($data);
            } else {
                return $this->update($data['id'], $data);
            }
        } catch (\Exception $e) {
            return null;
        }
    }

    public function getList($request)
    {
        $title = $request->get('title');
        $category_id = $request->get('category_id');
        $status = $request->get('status', -1);
        $created_at = $request->get('created_at');

        $news = $this->_model->where('id', '>=', 1);

        if($title) {
            $news->where('title', 'LIKE', "%$title%");
        }

        if($category_id > 0) {
            $news->where('category_id', $category_id);
        }
        if($status >= 0) {
            $news->where('status', $status);
        }

        if($created_at) {
            $created_at = explode('-', $created_at);
            $news->where('created_at', '>=', date("Y-m-d H:i:s", strtotime(str_replace("/", "-", trim($created_at[0])))));
            $news->where('created_at', '<=', date("Y-m-d  H:i:s", strtotime(str_replace("/", "-", trim($created_at[1])))));
        }

        $news->orderBy('id', 'DESC');

        return $news->paginate(Constant::ITEM_PER_PAGE);
    }

    public function getLatestNews($limit = 5, $category_id = 0, $categoryRepository = null)
    {
        $this->setModel();
        $news = $this->initNewsORM()
            ->orderBy('id', 'DESC')
            ->offset(0)
            ->limit($limit)
        ;

        if($category_id > 0 && $categoryRepository) {
            //Child categories
            $catIDs = [$category_id];
            $categoryChilds = $categoryRepository->getCategoriesByParentId($category_id);
            foreach ($categoryChilds as $categoryChild) {
                $catIDs[] = $categoryChild->id;
            }

            //Tags
            $newsId = Tag::whereIn('category_id', $catIDs)->pluck('news_id');
            $news = $news->whereIn('category_id', $catIDs)->orWhereIn('id', $newsId);
        }

        return $news->get();
    }

    public function removeNews($id, $request)
    {
        $check = $this->findFirstWhere(['user_id' => $id, 'path' => $request->input('path')]);
        if($check) {
            return $check->delete();
        }
        return false;
    }

    public function getNews($id, $request)
    {
        // TODO: Implement getNews() method.
    }
}
