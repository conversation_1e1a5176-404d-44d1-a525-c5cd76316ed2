<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Constant;
use App\Helpers\Permission;
use App\Helpers\Utils;
use App\Models\Advertisement;
use App\Models\Page;
use App\Repositories\Setting\SettingRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class SettingController extends Controller
{
    public $repository;
    public function __construct(SettingRepository $repository)
    {
        $this->repository = $repository;
    }

    public function index(Request $request, SettingRepository $settingRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_SETTING)) {
            return redirect(route('admin.admin_dashboard'));
        }

        if($request->isMethod('POST')) {
            $allData = $request->all();
            unset($allData['_token']);
            foreach ($allData as $key => $values) {
                $settingRepository->saveData($key, $values);
            }
            return redirect(route('admin.setting'));
        }
        $settingData = $settingRepository->getAll();
        $settings = [];
        if($settingData) {
            foreach ($settingData as $settingDatum) {
                $settings[$settingDatum->key] = $settingDatum->values;
            }
        }
        return view('admin.setting.index', compact('settings'));
    }

    public function contact(Request $request, SettingRepository $settingRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_SETTING)) {
            return redirect(route('admin.admin_dashboard'));
        }

        if($request->isMethod('POST')) {
            $data = $request->all();
            unset($data['_token']);
            $settingRepository->saveDataJSON([
                'key' => Constant::CONTACT_SETTING_KEY,
                'values' => $data
            ]);
        }
        $contactSetting = $settingRepository->getData(Constant::CONTACT_SETTING_KEY);
        if($contactSetting) {
            $contactSetting = $contactSetting->values;
        }
        return view('admin.setting.contact', compact('contactSetting'));
    }

    public function help(Request $request)
    {
        return view('admin.setting.help');
    }

    public function staticPageUpdate(Request $request, $id = 0)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_STATIC_PAGE)) {
            return redirect(route('admin.admin_dashboard'));
        }

        $staticPage = Page::find($id);
        if($request->isMethod('POST')) {
            $id = $request->get('id');
            $title = $request->get('title');
            $title_seo = $request->get('title_seo');
            $description = $request->get('description');
            $content = $request->get('content');

            if($title && $content) {
                $setting = Page::find($id);
                if($setting) {
                    $setting->title = $title;
                    $setting->title_seo = $title_seo;
                    $setting->description = $description;
                    $setting->content = $content;
                    $setting->slugify = Utils::slugify($title);
                    $setting->save();
                } else {
                    Page::create([
                        'title' => $title,
                        'title_seo' => $title_seo,
                        'description' => $description,
                        'content' => $content,
                        'slugify' => Utils::slugify($title),
                    ]);
                }
            }
            return redirect(route('admin.static_page'));
        }
        return view('admin.setting.static_page_update', compact('staticPage'));
    }

    public function staticPage(Request $request)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_STATIC_PAGE)) {
            return redirect(route('admin.admin_dashboard'));
        }

        $staticPages = Page::get();
        return view('admin.setting.static_page', compact('staticPages', 'request'));
    }

    public function staticPageSave(Request $request)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_STATIC_PAGE)) {
            return response()->json(['code' => 1, 'message' => 'Không đủ quyền thực hiện tác vụ này']);
        }

        $code = 0; $message = "OK";
        if($request->isMethod('POST')) {
            $id = $request->get('id');
            $field = $request->get('field');
            if($field == 'status') {
                $page = Page::find($id);
                if($page) {
                    $page->status = 1 - $page->status;
                    $page->save();
                }
            }
        }
        return response()->json(['code' => $code, 'message' => $message]);
    }

    public function advertisement(Request $request)
    {
        $data = Advertisement::get();
        return view('admin.setting.advertisement', compact('data', 'request'));
    }

    public function advertisementUpdateStatus(Request $request)
    {
        $code = 1;
        $message = 'Gặp một số lỗi, vui lòng thử lại!';

        $id = $request->get('id');
        $field = $request->get('field');

        $row = Advertisement::find($id);
        if($row) {
            $row->{$field} = 1 - $row->{$field};
            $row->save();

            $code = 0;
            $message = "OK";
            $data = ['status' => $row->status];
        }

        return response()->json(['code' => $code, 'message' => $message, 'data' => $data]);
    }

    public function advertisementDelete($id)
    {
        try {
            Advertisement::find($id)->delete();
        } catch (\Exception $e) {

        }
        return redirect(route('admin.advertisement'));
    }

    public function advertisementUpdate(Request $request)
    {
        $data = $request->all();
        if($data) {
            unset($data['_token']);

            if($data['id'] > 0) {
                $row = Advertisement::find($data['id']);
                $row->title = $data['title'];
                $row->photo = $data['photo'];
                $row->redirect_url = $data['redirect_url'];
                $row->status = !empty($data['status']) ? $data['status'] : 0;
                $row->sort = $data['sort'];
                $row->position = $data['position'];
                $row->save();
            } else {
                Advertisement::create($data);
            }
        }

        return redirect(route('admin.advertisement'));
    }
}
