<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Permission;
use App\Models\Group;
use App\Models\PermissionGroup;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class PermissionController extends Controller
{
    public function index(Request $request)
    {
        if(!Permission::isAdmin()) {
            return redirect(route('admin.admin_dashboard'));
        }

        $users = User::all();
        $groups = Group::all();

        $permissions = PermissionGroup::leftJoin('users', 'users.id', '=', 'permission_groups.user_id')
            ->leftJoin('groups', 'groups.id', '=', 'permission_groups.group_id')
            ->select('users.user_name', 'users.full_name', 'groups.group_name', 'permission_groups.*')
            ->get();

        $usersPermission = $groupsPermission = [];
        if($permissions) {
            foreach ($permissions as $permission) {
                if($permission->user_id > 0) {
                    $usersPermission[$permission->permission_key][] = $permission;
                } elseif ($permission->group_id) {
                    $groupsPermission[$permission->permission_key][] = $permission;
                }
            }
        }

        return view('admin.user.permission', compact('request', 'users', 'groups', 'usersPermission', 'groupsPermission'));
    }

    public function update(Request $request)
    {
        if(!Permission::isAdmin()) {
            return response()->json(['code' => 1, 'message' => 'Không đủ quyền thực hiện tác vụ này']);
        }

        $user_ids = $request->get('user_id');
        $group_ids = $request->get('group_id');
        $permisson_key =  $request->get('permission_key');
        if($permisson_key) {
            PermissionGroup::where('permission_key', $permisson_key)->delete();
            if($user_ids) {
                foreach ($user_ids as $user_id) {
                    PermissionGroup::create(['permission_key' => $permisson_key, 'user_id' => $user_id]);
                }
            }
            if($group_ids) {
                foreach ($group_ids as $group_id) {
                    PermissionGroup::create(['permission_key' => $permisson_key, 'group_id' => $group_id]);
                }
            }
        }       
        
        return response()->json(['code' => 0, 'message' => 'OK']);
    }

    public function remove(Request $request)
    {
        if(!Permission::isAdmin()) {
            return response()->json(['code' => 1, 'message' => 'Không đủ quyền thực hiện tác vụ này']);
        }

        if($id = $request->get('id')) {
            PermissionGroup::find($id)->delete();
        }
        return response()->json(['code' => 0, 'message' => 'OK']);
    }
}
