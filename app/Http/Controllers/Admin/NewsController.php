<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Constant;
use App\Helpers\Permission;
use App\Helpers\Utils;
use App\Models\Category;
use App\Models\Tag;
use App\Repositories\Category\CategoryRepository;
use App\Repositories\News\NewsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Session;

class NewsController extends Controller
{
    public function index(Request $request, NewsRepository $newsRepository, CategoryRepository $categoryRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_NEWS)) {
            return redirect(route('admin.admin_dashboard'));
        }

        $categoriesActive = $categoryRepository->getCategoriesActive(1);
        $categories = Utils::formatCategories($categoriesActive);
        $mapsCategory = Utils::mapsCategory($categories, 'id', 'category_title');
        $news = $newsRepository->getList($request);
        return view('admin.news.index', compact('request', 'news', 'categories', 'mapsCategory'));
    }

    public function edit(Request $request, $id = 0, CategoryRepository $categoryRepository, NewsRepository $newsRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_NEWS)) {
            return redirect(route('admin.admin_dashboard'));
        }

        $categoriesActive = $categoryRepository->getCategoriesActive(1);
        $categories = Utils::formatCategories($categoriesActive);

        $news = $newsRepository->find($id);

        $categoryTitleAll = Category::where('status', 1)->pluck('category_title');
        unset($categoriesActive);
        unset($categoryGroups);
        return view('admin.news.update', compact('categories', 'news', 'categoryTitleAll'));
    }

    public function update(Request $request, CategoryRepository $categoryRepository, NewsRepository $newsRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_NEWS)) {
            return redirect(route('admin.admin_dashboard'));
        }

        $id = $request->get('id', 0);
        $photo = $request->get('photo');

        //Get old image
        $news = $newsRepository->find($id);
        $image = $photo;
        if(!$photo) {
            $image = $news ? $news->photo : '';
        }

        //Check image
        if(!$image) {
            Session::flash('alert-danger', 'Ảnh chưa hợp lệ, vui lòng cập nhật lại');
            return redirect()->back();
        }

        //Process title
        $titles = $request->get("title");

        if(!$titles) {
            Session::flash('alert-danger', 'Vui lòng nhập tiêu đề tin tức');
            return redirect()->back();
        }

        //Process description
        $descriptions = $request->get("description");

        if(!$descriptions) {
            Session::flash('alert-danger', 'Vui lòng nhập mô tả ngắn tin tức');
            //return redirect()->back();
        }

        //Process content
        $contents = $request->get("content");

        if(!$contents) {
            Session::flash('alert-danger', 'Vui lòng nhập nội dung tin tức');
            return redirect()->back();
        }

        $date_public = trim($request->get('date_public'));
        $date_end = trim($request->get('date_end'));

        if($date_public) {
            $date_public = str_replace('01/01/1970', '', $date_public);
        }

        if($date_end) {
            $date_end = str_replace('01/01/1970', '', $date_end);
        }

        $tags = $request->get('tags');
        $categoriesTag = $tags ? $categoryRepository->findWhereIn('category_title', explode(',', $tags)) : [];

        $newsData = [
            'title' => ($titles),
            'slugify' => Utils::slugify($titles),
            'content' => ($contents),
            'description' => ($descriptions),
            'category_id' => $request->get('category_id', 0),
            'title_seo' => $request->get('title_seo'),
            'description_seo' => $request->get('description_seo'),
            'keyword_seo' => $request->get('keyword_seo'),
            'date_public' => $date_public ? date("Y-m-d H:i:s", strtotime(str_replace("/", "-", $date_public))) : null,
            'date_end' => $date_end ? date("Y-m-d H:i:s", strtotime(str_replace("/", "-", $date_end))) : null,
            'status' => $request->get('status'),
            'author' => $request->get('author'),
            'is_hot' => $request->get('is_hot'),
            'is_focus' => $request->get('is_focus'),
            'photo' => $image,
            'tags' => $tags,
        ];
        if($id > 0 && $news) {
            $result = $newsRepository->update($id, $newsData);
        } else {
            $result = $newsRepository->create($newsData);
        }

        if(!$result) {
            Session::flash('alert-danger', 'Cập nhật bài viết gặp vấn đề, vui lòng nhập lại');
            return redirect()->back();
        }

        //Add tags
        Tag::where('news_id', $result->id)->delete();
        if($categoriesTag) {
            foreach ($categoriesTag as $item) {
                Tag::create(['category_id' => $item->id, 'news_id' => $result->id]);
            }
        }

        return redirect(route('admin.news'));
    }

    public function updateByField(Request $request, NewsRepository $newsRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_NEWS)) {
            return response()->json(['code' => 1, 'message' => 'Không đủ quyền thực hiện tác vụ này']);
        }

        $code = 1;
        $message = 'Gặp một số lỗi, vui lòng thử lại!';
        $data = [];

        $id = $request->get('id');
        $field = $request->get('field');

        $news = $newsRepository->find($id);
        if($news) {
            $news->{$field} = 1 - $news->{$field};
            $news->save();

            $code = 0;
            $message = "OK";
            $data = ['status' => $news->status];
        }

        return response()->json(['code' => $code, 'message' => $message, 'data' => $data]);
    }
}
