<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Constant;
use App\Helpers\Permission;
use App\Models\User;
use App\Repositories\User\UserRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use function Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    public function __construct(Request $request)
    {

    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_USER_LIST)) {
            return redirect(route('admin.admin_dashboard'));
        }

        $user = Auth::user();
        $name = $request->get('name');
        $email = $request->get('email');
        $phone = $request->get('phone');
        $users = User::where('is_role', '>=', 0);
        if($name) {
            $users->where('name', 'like', '%'.$name.'%');
        }
        if($email) {
            $users->where('email', $email);
        }
        if($phone) {
            $users->where('phone', $phone);
        }
        $users = $users->paginate(Constant::ITEM_PER_PAGE);
        return view('admin.user.index', compact('request', 'user', 'users'));
    }

    public function staff(Request $request)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_USER_LIST)) {
            return redirect(route('admin.admin_dashboard'));
        }

        $user = Auth::user();
        $name = $request->get('name');
        $email = $request->get('email');
        $phone = $request->get('phone');
        $users = User::where('is_role', '>=', 0);
        if($name) {
            $users->where('name', 'like', '%'.$name.'%');
        }
        if($email) {
            $users->where('email', $email);
        }
        if($phone) {
            $users->where('phone', $phone);
        }
        $users = $users->paginate(Constant::ITEM_PER_PAGE);
        return view('admin.user.staff', compact('request', 'user', 'users'));
    }

    /**
     * @param Request $request
     * @param int $id
     * @param UserRepository $userRepository
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, UserRepository $userRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_USER_LIST)) {
            return response()->json(['code' => 1, 'message' => 'Không đủ quyền thực hiện tác vụ này']);
        }

        $response = ['code' => 1, 'message' => 'Có vấn đề khi cập nhật', 'data' => []];
        $id = $request->get('user_id', 0);
        $password = $request->get('password');

        if($id > 0) {
            $user = $userRepository->find($id);
        } else {
            if(!$password) {
                $response = ['code' => 1, 'message' => 'Có vấn đề khi cập nhật, vui lòng nhập lại mật khẩu!', 'data' => []];
                return response()->json($response);
            }
            $user = new User();
        }
        if($request->isMethod('POST')) {
            try {
                $user->full_name = $request->get('full_name');
                $user->user_name = $request->get('user_name');
                $user->phone = $request->get('phone');
                $user->email = $request->get('email');
                $is_role = $request->get('is_role');
                $is_active = $request->get('is_active', 1);

                $user->is_active = $is_active;
                $user->is_role = $is_role;
                $user->avatar = '/images/avatar_default.png';

                if($password) {
                    $user->password = \Hash::make($password);
                }

                $user->save();
                $response = ['code' => 0, 'message' => 'OK', 'data' => []];
            } catch (\Exception $e) {
                $response = ['code' => 1, 'message' => 'Có vấn đề khi cập nhật, vui lòng kiểm tra lại email, username có thể đã tồn tại rồi!', 'data' => []];
            }
        }

        return response()->json($response);
    }

    public function updateStatus(Request $request, UserRepository $repository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_USER_LIST)) {
            return response()->json(['code' => 1, 'message' => 'Không đủ quyền thực hiện tác vụ này']);
        }

        $id = $request->get('id', 0);

        $user = $repository->find($id);
        if($id > 0) {
            $user->is_active = 1 - $user->is_active;
            $user->save();
        }
        return response()->json(['code' => 0, 'message' => 'OK', 'data' => $user]);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }

    public function list(Request $request)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_USER_LIST)) {
            return response()->json(['code' => 1, 'message' => 'Không đủ quyền thực hiện tác vụ này']);
        }

        $name = $request->query('search', null);
        $users = User::where('is_role', '>=', 0)->when($name, function ($query, $name) {
            return $query->where('full_name', 'like', '%' . $name . '%');
        })->select('id', 'full_name')->get();
        return response()->json(['code' => 0, 'message' => 'OK', 'data' => $users]);
    }
}
