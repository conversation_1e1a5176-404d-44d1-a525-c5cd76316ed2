<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\Constant;
use App\Helpers\Permission;
use App\Helpers\Utils;
use App\Repositories\Category\CategoryRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class CategoryController extends Controller
{
    public function index(Request $request, CategoryRepository $categoryRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_CATEGORY)) {
            return redirect(route('admin.admin_dashboard'));
        }

        $categories12 = $categoryRepository->getCategoriesLevel12();
        $categories = Utils::formatCategories($categories12);
        unset($categories12);
        return view('admin.category.index', compact( 'categories'));
    }

    public function update(Request $request, CategoryRepository $categoryRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_CATEGORY)) {
            return response()->json(['code' => 1, 'message' => 'Không đủ quyền thực hiện tác vụ này']);
        }

        $category_level = 1;
        $router = $request->get('router', 0);
        $id = $request->get('id', 0);
        $parent_id = $request->get('parent_id');
        $photo = $request->get('photo');

        //Get level for this category
        if($parent_id > 0) {
            $parent = $categoryRepository->find($parent_id);
            $category_level = $parent->category_level + 1;
        }
        $image = $photo;
        //Get old image
        $category = $categoryRepository->find($id);
        if(!$photo) {
            $image = $category ? $category->photo : '';
        }

        //Process category title
        $categoryTitles = $request->get("category_title");

        $categoryData = [
            'category_title' => ($categoryTitles),
            'slugify' => Utils::slugify($categoryTitles),
            'parent_id' => $request->get('parent_id', 0),
            'category_description' => $request->get('category_description'),
            'category_title_seo' => $request->get('category_title_seo'),
            'category_description_seo' => $request->get('category_description_seo'),
            'category_keyword_seo' => $request->get('category_keyword_seo'),
            'redirect_link' => $request->get('redirect_link'),
            'status' => $request->get('status'),
            'sort' => $request->get('sort'),
            'menu_top' => $request->get('menu_top'),
            'left_menu' => $request->get('left_menu'),
            'hot' => $request->get('hot'),
            'photo' => $image,
            'category_level' => $category_level,
            'type' => $router == Constant::CATEGORY_DOCUMENT_ROUTER ? 2 : 1,
        ];
        if($id > 0 && $category) {
            $result = $categoryRepository->update($id, $categoryData);
        } else {
            $result = $categoryRepository->create($categoryData);
        }

        return response()->json(['code' => 0, 'message' => 'OK', 'data' => $result]);
    }

    public function getChildren(Request $request, CategoryRepository $categoryRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_CATEGORY)) {
            return response()->json(['code' => 1, 'message' => 'Không đủ quyền thực hiện tác vụ này']);
        }

        $htmlData = "";
        $id = $request->get('id', 0);
        $level = $request->get('level', 2);

        $categoryCurrent = $categoryRepository->find($id);
        $categories = $categoryRepository->getCategoriesByParentId($id);
        if($categories) {
            $categories = Utils::formatCategories($categories, $id);

            //Đoạn này code VCL :(
            foreach ($categories as $category) {
                $bgDanger = $category->status == 1 ? '':'bg-danger';
                $htmlData .= "<li id='cat-item-{$category->id}' onclick=\"Category.children({$category->id}, ".($level+1).")\" style=\"cursor: pointer\" class=\"list-group-item d-flex list-group-item-action justify-content-between align-items-center\">"
                    . ($category->category_title)
                    . "<span onclick='return Category.statusChange({$category->id}, this)' title='Nhấn đúp chuột để Bật hoặc Tắt danh mục' class=\"badge badge-primary badge-pill $bgDanger\">" . (!empty($category->children) ? count($category->children) : 0) . "</span>"
                    . "</li>";
            }
            unset($categoryGroups);
            unset($categories);
        }

        return response()->json(['code' => 0, 'message' => 'OK', 'data' => [
            'htmlData' => $htmlData ? $htmlData : "<li class='list-group-item'>Vui lòng chọn một danh mục cha</li>",
            'detailData' => $categoryCurrent
        ]]);
    }

    public function detail(Request $request, CategoryRepository $categoryRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_CATEGORY)) {
            return response()->json(['code' => 1, 'message' => 'Không đủ quyền thực hiện tác vụ này']);
        }

        $id = $request->get('id', 0);
        $data = $categoryRepository->find($id);
        return response()->json(['code' => 0, 'message' => 'OK', 'data' => $data]);
    }

    public function updateByField(Request $request, CategoryRepository $categoryRepository)
    {
        if(!Permission::checkPermission(Constant::PERMISSION_CATEGORY)) {
            return response()->json(['code' => 1, 'message' => 'Không đủ quyền thực hiện tác vụ này']);
        }

        $code = 1;
        $message = 'Gặp một số lỗi, vui lòng thử lại!';
        $data = [];

        $id = $request->get('id');
        $field = $request->get('field');

        $category = $categoryRepository->find($id);
        if($category) {
            switch ($field) {
                case 'status':
                    $category->status = 1 - $category->status;
                    $category->save();

                    $code = 0;
                    $message = "OK";
                    $data = ['status' => $category->status];
                    break;

                default:
                    break;
            }
        }

        return response()->json(['code' => $code, 'message' => $message, 'data' => $data]);
    }
}
