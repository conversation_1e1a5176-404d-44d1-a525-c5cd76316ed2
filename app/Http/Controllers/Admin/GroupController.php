<?php

namespace App\Http\Controllers\Admin;

use App\Models\Group;
use App\Repositories\Group\GroupRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class GroupController extends Controller
{
    public function index(Request $request)
    {
        $name = $request->get('name', null);
        $groups = Group::with('users')->when($name, function ($query, $name) {
            return $query->where('groups.group_name', 'like', '%'. $name . '%');
        })->get();
        return view('admin.user.group', compact('request', 'groups'));
    }

    public function update(Request $request, $id = 0, GroupRepository $groupRepository)
    {
        $response = ['code' => 1, 'message' => 'Có vấn đề khi cập nhật', 'data' => []];
        if($id > 0) {
            $group = $groupRepository->find($id);
        } else {
            $group = new Group();
        }
        if($request->isMethod('POST') && $request->get('group_name')) {
            try {
                $group->group_name = $request->get('group_name');
                $group->status = $request->get('status', 1);
                $group->save();
                $data = route('admin.user_group');
                $response = ['code' => 0, 'message' => 'OK', 'data' => $data];
            } catch (\Exception $e) {
                $response = ['code' => 1, 'message' => 'Có vấn đề khi cập nhật, vui lòng kiểm tra lại tên nhóm có thể đã tồn tại rồi!', 'data' => []];
            }
        }
        return response()->json($response);
    }

    public function destroy($id, GroupRepository $groupRepository)
    {
        try {
            $groupRepository->find($id)->users()->detach();
            $groupRepository->delete($id);
            $response = ['code' => 0, 'message' => 'Deleted', 'data' => $id];
        } catch (\Exception $e) {
            $response = ['code' => 1, 'message' => 'Có vấn đề khi xóa nhóm', 'data' => []];
        }
        return response()->json($response);
    }

    public function updateStatus(Request $request, GroupRepository $repository)
    {
        $id = $request->get('id', 0);
        $group = $repository->find($id);
        if($id > 0) {
            $group->status = 1 - $group->status;
            $group->save();
        }

        return response()->json(['code' => 0, 'message' => 'OK', 'data' => $group]);
    }

    public function addUserToGroup($id, GroupRepository $groupRepository)
    {
        $group = $groupRepository->find($id);
        $users = request()->input('userIds');
        $group->users()->sync($users, false);

        return response()->json(['code' => 0, 'message' => 'OK', 'data' => $group->users()->allRelatedIds()]);
    }

    public function removeUserFromGroup($id, GroupRepository $groupRepository)
    {
        $group = $groupRepository->find($id);
        $users = request()->input('userId');
        $group->users()->detach([$users]);
        return response()->json(['code' => 0, 'message' => 'OK', 'data' => $group->users()->allRelatedIds()]);
    }
}
