<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\RemoteV4U;
use App\Helpers\Utils;
use App\Models\Category;
use App\Models\News;
use App\Models\Tag;
use App\Models\Viewed;
use App\Repositories\News\NewsRepository;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\FromCollection;

class DashboardController extends Controller
{
    //

    public function index(Request $request, NewsRepository $newsRepository)
    {
        //SUM all
        $totalCounter = $newsRepository->initNewsORM()->select(DB::raw('SUM(viewed) as total_counter'))->first();

        //SUM by week
        $currentWeek = Utils::getRangThisWeek();
        $totalCounterByCurrentWeek = Viewed::select(DB::raw('SUM(counter) as total_counter'))->where('created_at', '>=', $currentWeek['start_time'])->where('created_at', '<=', $currentWeek['end_time'])->first();

        //SUM by day
        $totalCounterByCurrentDay = Viewed::select(DB::raw('SUM(counter) as total_counter'))->whereDate('created_at', date('Y-m-d'))->first();

        $totalCounters = (object)[
            'day' => $totalCounterByCurrentDay->total_counter,
            'week' => $totalCounterByCurrentWeek->total_counter,
            'all' => $totalCounter->total_counter,
        ];

        //get top viewed
        $topViewed = Viewed::select("news_id", "counter", "vieweds.created_at", "news.title")->join('news', 'news.id', '=', 'vieweds.news_id')
            ->whereDate('vieweds.created_at', date('Y-m-d'))->orderBy('counter', 'DESC')->get();

        return view('admin.dashboard.index', compact('request', 'totalCounters', 'totalCounterByCurrentDay', 'currentWeek', 'topViewed'));
    }

    public function updateLeft()
    {
        $news1 = ["Phật giáo thanh hóa", "Phật giáo Yên Định", "Phật giáo Triệu Sơn", "Phật giáo Thiệu Hóa", "Phật giáo Nga Sơn", "Phật giáo Hà Trung", "Phật giáo Hậu Lộc", "Phật giáo Hoằng Hóa", "Phật giáo Quảng Xương", "Phật giáo Đông Sơn", "Phật giáo Bỉm Sơn", "Ban giáo dục tăng ni", "Ban hướng dẫn phật tử", "Ban hoằng pháp", "Ban kinh tế tài chính", "Ban thông tin truyền thông", "Ban nghi lễ", "Ban phật giáo quốc tế", "Ban từ thiện xã hội", "Ban kiểm soát", "Ban pháp chế"];
        $news2 = ["Ban giáo dục tăng ni", "Ban hướng dẫn phật tử", "Ban hoằng pháp", "Ban kinh tế tài chính", "Ban thông tin truyền thông", "Ban nghi lễ", "Ban phật giáo quốc tế", "Ban từ thiện xã hội", "Ban kiểm soát", "Ban pháp chế"];
    }

    public function convertTags()
    {
        $news = News::get();
        foreach ($news as $item) {
            try {
                $tags = json_decode($item->tags);
                $item->tags = implode(',', $tags);
                $item->save();
            } catch (\Exception $e) {

            }
        }
    }

    public function updateTags()
    {
        $news = News::get();
        foreach ($news as $item) {
            try {
                $tags = ($item->tags);
                if($tags) {
                    $categoriesTag = Category::whereIn('category_title', explode(',', $tags))->get();
                    //Add tags
                    //Tag::where('news_id', $item->id)->delete();
                    if($categoriesTag) {
                        foreach ($categoriesTag as $cat) {
                            Tag::create(['category_id' => $cat->id, 'news_id' => $item->id]);
                        }
                    }
                }

            } catch (\Exception $e) {

            }
        }
    }

    function hexToStr($hex){
        $string = '';
        for ($i = 0; $i < strlen($hex) - 1; $i += 2){
            $string .= chr(hexdec($hex[$i].$hex[$i + 1]));
        }
        return $string;
    }

    public function bazaarvoice(Request $request)
    {
        // Hex decode shared secret key
        $sharedSecretKey = $this->hexToStr('1a1424649645a97bc65f74f2e21412f8');

        // Initialize Crypt_AES class
        $cipher = new \Crypt_AES(CRYPT_AES_MODE_ECB);
        $cipher->setKeyLength(128);
        $cipher->setKey($sharedSecretKey);

        $list = file_get_contents("https://api.bazaarvoice.com/notifications/aeg-en_au/subscriptions/by_page/PIE/OPT_OUT?passkey=na2f919ef3d0f7e6f6d9d4831479e06cfd6fe74639c71eac10ef63a41278847af9&limit=1000&offset=NWRjY2EwM2Q1NWE1OGVjZjliYzU1ZTIw");

        if($list) {
            $list = json_decode($list);
            $offset = $list->offset;
            $data = $list->data;
            $emails = [];
            foreach ($data as $row) {
                $encryptedEmail = $this->hexToStr($row->emailAddress);
                $timestamp = $row->timestamp;

                // Perform decryption
                $decryptedEmail = $cipher->decrypt($encryptedEmail);

                $emails[] = ['email' => $decryptedEmail, 'timestamp' => $timestamp];
            }

            $headers = [
                "Content-type" => "text/csv",
                "Content-Disposition" => "attachment; filename=file.csv",
                "Pragma" => "no-cache",
                "Cache-Control" => "must-revalidate, post-check=0, pre-check=0",
                "Expires" => "0"
            ];
            $callback = function() use ($emails) {
                $FH = fopen('php://output', 'w');
                foreach ($emails as $email) {
                    fputcsv($FH, $email);
                }
                fclose($FH);
            };
            return response()->stream($callback, 200, $headers);
        }
    }


    public function crawl(Request $request)
    {
        $remoteV4U = new RemoteV4U();
        $home = $remoteV4U->home();
        $tk = ""; $login = true;
        if(!$home) {
            $preLogin = $remoteV4U->preLogin();
            if ($preLogin) {
                $tk = $preLogin;
                $login = $remoteV4U->login($tk);
            }
        } else {
            $tk = $home;
        }

        //$remoteV4U->logout(['rt' => 'logout', 'tk' => $tk]);

        if($login) {
            //$news = $this->news($remoteV4U, $tk);
            //$this->newsDetail($remoteV4U, $tk);
            //$this->category($remoteV4U, $tk);

            //$this->downloadNewsImage();
            //$this->downloadCategoryImage();
            //$this->downloadBigNewsImage();

            //$this->tags();
        }
    }

    public function updateSlugify()
    {
        $cats = News::all();
        foreach ($cats as $cat) {
            if(!$cat->slugify) {
                $cat->slugify = Utils::slugify($cat->title);
                $cat->save();
            }
        }
    }

    public function news($remoteV4U, $tk)
    {
//        $news = $remoteV4U->news();
//        if(!$tk) {
//            preg_match("/tk:'([^']+)'/", $news,$match);
//            if(!empty($match[1])) {
//                $tk = $match[1];
//            }
//        }
        $remoteV4U->newsNextPageGetView($tk);
    }

    /**
     * @
     * @param $remoteV4U
     * @param $tk
     */
    public function newsDetail($remoteV4U, $tk)
    {
        /** @var $remoteV4U RemoteV4U */
        $news = News::pluck('id');
        $existed = $news->toArray();
        $newsID = file_get_contents(storage_path('app/news_id.txt'));
        $newsID = explode("\n", $newsID);
        $newsID = array_values(array_diff($newsID, $existed));

        foreach ($newsID as $count => $id) {
            if($count % 10 == 0) {
                sleep(1);
            }
            $urlDetail = "https://groot.phatgiaothanhhoa.com/news/edit/{$id}";
            $remoteV4U->newsDetail($id, $urlDetail);
        }
    }

    public function category($remoteV4U, $tk)
    {
        $remoteV4U->category($tk);
    }

    public function updateViewed()
    {
        $newsID = file_get_contents(storage_path('app/news_id_viewed.txt'));
        $newsIDs = explode("\n", $newsID);

        foreach ($newsIDs as $n) {
            if ($n) {
                $rows = explode("||", $n);
                $news = News::find($rows[0]);
                if($news) {
                    $news->viewed = $rows[1];
                    $news->save();
                }
            }
        }
    }

    public function downloadBigNewsImage()
    {
        $newsID = file_get_contents(storage_path('app/news_id_img.txt'));
        $newsIDs = explode("\n", $newsID);

        $imgMaps = [];
        foreach ($newsIDs as $n) {
            if($n) {
                $rows = explode("||", $n);
                $imgMaps[$rows[0]] = str_replace(['/t-', '/t2-'], '/s-', $rows[1]);
            }
        }

        $offset = 0;
        $limit = 500;
        $totalNews = News::count();
        while (1) {
            //$news = News::orderBy('id', 'DESC')->offset($offset)->limit($limit)->where('id', '<', '3149 ')->get();
            $news = News::orderBy('id', 'DESC')->offset($offset)->limit($limit)->where('id', '<=', '2416')->get();
            if(!$news) {
                break;
            }

            foreach ($news as $item) {
                if(empty($imgMaps[$item->id])) {
                    continue;
                }

                $fname = basename($item->photo);
                $localPath = substr(str_replace($fname, '', $item->photo), 1);
                @mkdir($localPath, '755', true);

                $filename = basename($imgMaps[$item->id]); // To get file name

                $path = public_path($localPath) . $filename;
                @unlink($path);
                Utils::downloadImageFromUrl($path, $imgMaps[$item->id]);
                echo "<br />$item->id : " . $path;

            }

            $offset += $limit;

            if($offset > $totalNews) {
                break;
            }
            //sleep(1);
        }

    }

    public function downloadNewsImage()
    {
        $offset = 0;
        $limit = 500;
        $totalNews = News::count();
        while (1) {
            $news = News::orderBy('id', 'DESC')->offset($offset)->limit($limit)->where('id', '<', '1884')->get();
            if(!$news) {
                break;
            }

            foreach ($news as $item) {
                $localPath = "uploads/news/";
                if($item->date_public) {
                    $localPath = $localPath . date("Y/m/d", strtotime($item->date_public));
                } else {
                    $localPath = $localPath . date("Y/m/d");
                }
                @mkdir($localPath, '755', true);
                $images = $maps = [];

                if($item->photo && filter_var($item->photo, FILTER_VALIDATE_URL)) {
                    $images[] = $item->photo;
                }

                if($item->content) {
                    $images = array_merge($images, Utils::extraImageUrls($item->content));
                }

                if($images) {
                    foreach ($images as $count => $image) {
                        $filename = basename($image); // To get file name

                        $path = public_path($localPath) . '/' . $filename;
                        //if (!file_exists($path)) {
                            Utils::downloadImageFromUrl($path, $image);
                        //}
                        $maps[$image] = $localPath . '/' . $filename;
                    }

                    $photo = $maps[$item->photo];
                    $content = str_replace($images, $maps, $item->content);

                    $item->photo = $photo;
                    $item->content = $content;
                    $item->save();
                }
            }

            $offset += $limit;

            if($offset > $totalNews) {
                break;
            }
            //sleep(1);
        }

    }

    public function downloadCategoryImage()
    {
        $offset = 0;
        $limit = 200;
        $count = Category::count();
        while (1) {
            $categories = Category::orderBy('id', 'DESC')->offset($offset)->limit($limit)->get();
            if(!$categories) {
                break;
            }
            $localPath = "uploads/categories/";
            $localPath = $localPath . date("Y/m/d");
            @mkdir($localPath, '755', true);

            foreach ($categories as $item) {
                if($item->photo && filter_var($item->photo, FILTER_VALIDATE_URL)) {
                    $image = $item->photo;
                    $filename = basename($image); // To get file name

                    $path = public_path($localPath) . '/' . $filename;
                    Utils::downloadImageFromUrl($path, $image);
                    $item->photo = "";
                }
            }

            $offset += $limit;

            if($offset > $count) {
                break;
            }
            sleep(1);
        }

    }

    public function tags()
    {

    }
}
