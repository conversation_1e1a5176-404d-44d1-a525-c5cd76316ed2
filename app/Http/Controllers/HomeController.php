<?php

namespace App\Http\Controllers;

use App\Helpers\Utils;
use App\Models\Page;
use App\Repositories\Category\CategoryRepository;
use App\Repositories\News\NewsRepository;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     */
    public function __construct()
    {

    }

    /**
     * Show the application dashboard.
     *
     * @param Request $request
     * @param NewsRepository $newsRepository
     * @param CategoryRepository $categoryRepository
     * @return \Illuminate\Contracts\Support\Renderable
     * SELECT * FROM news WHERE created_at >= IFNULL(date_public, "2001-01-01") AND created_at <= IFNULL(date_end, NOW())
     */
    public function index(Request $request, NewsRepository $newsRepository, CategoryRepository $categoryRepository)
    {
        //latest news (5)
        $latestNews = $newsRepository->getLatestNews();

        //hot categories
        $hotCategories = $categoryRepository->getHotCategories();

        $per_page_news_new = Utils::getSetting('per_page_news_new');

        if($hotCategories) {
            foreach ($hotCategories as $hotCategory) {
                //get 5 news for each the category
                $news = $newsRepository->getLatestNews($per_page_news_new * 2, $hotCategory->id, $categoryRepository);
                $hotCategory->news = $news;
            }
        }

        //Viewed
        $viewedNews = $newsRepository->initNewsORM()->orderBy('viewed', 'DESC')->limit($per_page_news_new)->get();

        //hot news
        $hotNews = $newsRepository->initNewsORM()->where('is_hot', 1)
            ->orderBy('id', 'DESC')->limit($per_page_news_new)->get();

        return view('frontend.home.index', compact('hotNews', 'latestNews', 'hotCategories', 'viewedNews', 'hotNews', 'request'));
    }

    public function staticPage(Request $request, $page_title = null)
    {
        $page = Page::where('slugify', $page_title)->first();
        return view('frontend.static.index', compact('page', 'request'));
    }

    public function help()
    {

    }
}

