<?php

namespace App\Http\Controllers;

use App\Helpers\Utils;
use App\Models\Category;
use App\Models\News;
use App\Models\Tag;
use App\Repositories\Category\CategoryRepository;
use App\Repositories\News\NewsRepository;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    public function index(Request $request, $category_title, CategoryRepository $categoryRepository, NewsRepository $newsRepository)
    {
        $keyword = @trim($request->get('km'));
        $searchOption = $request->get('search');
        $category = null;
        if($keyword || $searchOption) {
            if($searchOption && !$keyword) {
                return redirect('/');
            }

            if($searchOption == 'news') {
                $allNews = $newsRepository->initNewsORM()->whereRaw("MATCH (title,content) AGAINST ('\"{$keyword}\" @4' IN BOOLEAN MODE)");
            } else {
                $allNews = $newsRepository->initNewsORM()->whereRaw("MATCH (author) AGAINST ('\"{$keyword}\" @4' IN BOOLEAN MODE)");
            }
        } else {
            $category = Category::where('slugify', $category_title)->first();
            if (!$category) {
                return redirect('/');
            }

            $categoryChilds = $categoryRepository->getCategoriesByParentId($category->id);

            //Child categories
            $catIDs = [$category->id];
            foreach ($categoryChilds as $categoryChild) {
                $catIDs[] = $categoryChild->id;
            }

            //Tags
            $newsId = Tag::whereIn('category_id', $catIDs)->pluck('news_id');

            $allNews = $newsRepository->initNewsORM()->whereIn('category_id', $catIDs)->orWhereIn('id', $newsId)->orderBy('id', 'DESC');
        }
        $allNews = $allNews->paginate(Utils::getSetting('per_page_category'));
        //latest news (10)
        $latestNews = $newsRepository->getLatestNews(10);
        $leftCategories = $categoryRepository->getLeftCategories();

        return view('frontend.news.index', compact('leftCategories', 'latestNews', 'allNews', 'request', 'category'));
    }

    public function detail(Request $request, $title, CategoryRepository $categoryRepository, NewsRepository $newsRepository)
    {
        $news = News::where('slugify', $title)->first();

        if(!$news) {
            return redirect('/');
        }
        $id = $news->id;

        //Category
        $category = Category::find($news->category_id);

        $catIDs = [];
        if($category) {
            //Child categories
            $categoryChilds = $categoryRepository->getCategoriesByParentId($category->parent_id);
            $catIDs = [$category->parent_id];
            foreach ($categoryChilds as $categoryChild) {
                $catIDs[] = $categoryChild->id;
            }
        }

        //Tags
        $newsId = [];
        if($catIDs) {
            $newsId = Tag::whereIn('category_id', $catIDs)->pluck('news_id')->toArray();
        } else {
            $catIDs = Tag::where('news_id', $id)->pluck('category_id')->toArray();
            $newsId = Tag::whereIn('category_id', $catIDs)->pluck('news_id')->toArray();
        }
        if($newsId) {
            $newsId = array_filter($newsId, function($n) use ($id){
                return $n != $id;
            });
        }

        $otherNews = $newsRepository->initNewsORM()->whereIn('category_id', $catIDs)->where('id', '!=', $id)->orWhereIn('id', $newsId);
        $otherNews = $otherNews->orderBy('id', 'DESC')->limit(Utils::getSetting('other'))->get();

        return view('frontend.news.detail', compact('news', 'otherNews'));
    }

    public function viewed(Request $request, $news_id)
    {
        $news = News::find($news_id);
        if($news) {
            Utils::viewed($request, $news);
        }
        return response()->json(['code' => 0, 'message' => 'OK']);
    }
}
