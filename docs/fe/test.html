<div id="fb-root"></div>
<script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v9.0&appId=330436488158794&autoLogAppEvents=1" nonce="LuQS1yff"></script>

<div class="fb-page" data-href="http://www.facebook.com/BanTTTTPhatgiaoThanhHoa" data-tabs="timeline" data-width="345" data-height="" data-small-header="true" data-adapt-container-width="true" data-hide-cover="false" data-show-facepile="true"><blockquote cite="http://www.facebook.com/BanTTTTPhatgiaoThanhHoa" class="fb-xfbml-parse-ignore"><a href="https://www.facebook.com/BanTTTTPhatgiaoThanhHoa">Phật giá<PERSON> Than<PERSON> Hó<PERSON></a></blockquote></div>

<script>
    var pageRegistry = {
        genericPDP: {
            key:'generic-pdp',
            name:'PDPs',
            regex: [/(cooking|fridges-and-freezers|dishwashers)\/([a-zA-Z0-9-])*\/([a-zA-Z0-9-])*\/$/g]
        },
        genericCDP: {
            key:'generic-cdp',
            name:'CDPs',
            regex: [/(cooking|fridges-and-freezers|dishwashers)\/([a-zA-Z0-9-])*\/$/g]
        },
        searchResultPage: {
            key:'search',
            name:'Search results page',
            regex: [/search/g]
        },
        promotionPage: {
            key:'promotion',
            name:'Promotions pages',
            regex: [/promotions/g]
        },
        contactUsPage: {
            key:'contact-us',
            name:'Contact us pages',
            regex: [/support\/contact-us/g]
        },
        wheretobuyPage: {
            key:'where-to-buy',
            name:'Find a Store Page',
            regex: [/.*\/where-to-buy/g]
        },
        wishlistPage: {
            key:'wishlist',
            name:'Wishlist Page',
            regex: [/wishlist/g]
        },
        comparePage: {
            key:'compare',
            name:'Compare Page',
            regex: [/([a-zA-Z0-9-])*\/([a-zA-Z0-9-])*\/compare\/([a-zA-Z0-9-])*/g]
        },
        serviceCentresPage: {
            key: 'service-centres',
            name:'Westinghouse Service & Repairs Support pages',
            regex: [/support\/service-centres/g]
        },
        brochuresPage: {
            key:'brochures',
            name:'Brochures pages',
            regex: [/brochures/g]
        },
        commercialPage: {
            key:'commercial',
            name:'Commercial Appliances pages',
            regex: [/commercial\/landing/g]
        },
        productRegisterSuccessPage: {
            key:'product_success_registration',
            name:'Product Registration Success Your Westinghouse pages',
            regex: [/support\/register\/thanks/g]
        },
        productRegisterPage: {
            key:'product_registration',
            name:'Product Registration Your Westinghouse pages',
            regex: [/support\/register/g]
        },
        unglossyPage: {
            key:'unglossy',
            name:'Unglossy pages',
            regex: [/unglossy\/([a-zA-Z0-9-])*\/$/g]
        },
        buyingGuidePage: {
            key:'buying-guide',
            name:'Buying guide pages',
            regex: [/([a-zA-Z0-9-])*\/([a-zA-Z0-9-])*\/buying-guide/g]
        },
        supportPage: {
            key:'support',
            name:'Support pages',
            regex: [/support/g]
        },
        homePage: {
            key:'home',
            name:'Home page',
            regex: [/^\/$/g]
        },
        everyPage: {
            key:'any',
            name:'Every Page',
            regex: [/.*/g]
        }
    };

    var refLocatorMap = {
        main:'main',
        youMayAlsoLike:'youMayAlsoLike',
        list:'list',
        WTPPopup:'whereToBuyPopup',
        Popup:'Popup',
        topNav:'TopBarNav',
        searchAutoComplete:'searchBox',
        searchDetailPage:'searchResults',
        heroProduct:'heroProduct',
        secondNav:'secondNav',
        download:'downloadUsermanual'
    };

    var eventCategoryMapping = {
        Support: {
            category:'Support',
            actions: {
                clickCustomerSupportNumberHandler: {
                    event:'GAEvent.ClickCustomerSupportNumberHandler',
                    event_action:'Click Tel Link'
                },
                socialMediaHandler: {
                    event:'GAEvent.SocialMediaHandler',
                    event_action:'Click Social Media Icons'
                },
            }
        },
        Navigation: {
            category:'Navigation',
            actions: {
                clickPrimaryNavHandler: {
                    event:'GAEvent.ClickPrimaryNavHandler',
                    event_action:'Click Primary Nav'
                },
                clickTopNavHandler: {
                    event:'GAEvent.ClickTopNavHandler',
                    event_action:'Click Top Nav'
                },
                clickCategoryTitleHandler: {
                    event:'GAEvent.ClickCategoryTitleHandler',
                    event_action:'Click Homepage Category Tiles'
                },
                clickFootersHandler: {
                    event:'GAEvent.ClickFootersHandler',
                    event_action:'Click Footers'
                },
            }
        },
        Outbound: {
            category:'Outbound',
            actions: {
                outboundLinkHandler: {
                    event:'GAEvent.OutboundLinkHandler',
                    event_action:'This will be consistent with APAC sites'
                },
                error404Handler: {
                    event:'GAEvent.Error404Handler',
                    event_action:'Error 404'
                },
            }
        },
        AppliancesAndAccessories: {
            category:'AppliancesAndAccessories',
            actions: {
                productImpressionHandler: {
                    event:'GAEvent.ProductImpressionHandler',
                    event_action:'View Product'
                },
                clickProductHandler: {
                    event:'GAEvent.ClickProductHandler',
                    event_action:'Click Product'
                },
                productDetailsHandler: {
                    event:'GAEvent.ProductDetailsHandler',
                    event_action:'View Product Detail'
                },
                clickFindAStoreHandler: {
                    event:'GAEvent.ClickFindAStoreHandler',
                    event_action:'Find a store'
                },
                clickBuyOnlineHandler: {
                    event:'GAEvent.ClickBuyOnlineHandler',
                    event_action:'Find a store'
                },
                clickRetailerBuyNowHandler: {
                    event:'GAEvent.ClickRetailerBuyNowHandler',
                    event_action:'Click Retailer Buy Now'
                },
            }
        },
        Review: {
            category:'Review',
            actions: {
                productDetailReviewsHandler: {
                    event:'GAEvent.ProductDetailReviewsHandler',
                    event_action:'View Reviews'
                },
                clickWriteAReviewHandler: {
                    event:'GAEvent.ClickWriteAReviewHandler',
                    event_action:'Click Write Review'
                },
                submitReviewHandler: {
                    event:'GAEvent.SubmitReviewHandler',
                    event_action:'Submit Review Successfully'
                },
            }
        },
        ProductComparison: {
            category:'ProductComparison',
            actions: {
                addToCompareHandler: {
                    event:'GAEvent.AddToCompareHandler',
                    event_action:'Click Add To Compare'
                },
                startCompareHandler: {
                    event:'GAEvent.StartCompareHandler',
                    event_action:'Start Compare Page'
                },
            }
        },
        ProductFilter: {
            category:'ProductFilter',
            actions: {
                dimensionsUpdatedHandler: {
                    event:'GAEvent.DimensionsUpdatedHandler',
                    event_action:'Dimensions filter & change D'
                },
            }
        },
        Documents: {
            category:'Documents',
            actions: {
                downloadManualPDPHandler: {
                    event:'GAEvent.DownloadManualPDPHandler',
                    event_action:'Click Tel Link'
                },
                downloadManualSupportPageHandler: {
                    event:'GAEvent.DownloadManualSupportPageHandler',
                    event_action:'Download Manual Support Page'
                },
                downloadManualSearchResultHandler: {
                    event:'GAEvent.DownloadManualSearchResultHandler',
                    event_action:'Download Manual Search Result'
                },
                downloadBrochureHandler: {
                    event:'GAEvent.DownloadBrochureHandler',
                    event_action:'Download Brochure'
                },
            }
        },
        Videos: {
            category:'Videos',
            actions: {
                youtubeVideoPlayingHandler: {
                    event:'GAEvent.YoutubeVideoPlayingHandler',
                    event_action:'Click Video Play'
                },
                StartVideo: {
                    event: 'GAEvent.StartPlayYouTubeVideo',
                    event_action:'Play'
                },
                ProgressVideo: {
                    event: 'GAEvent.progressVideo',
                    event_action:'Progress - {percentage}',
                    event_track: [90, 75, 50, 25, 10]
                },
                CompleteVideo: {
                    event: 'GAEvent.CompletePlayYouTubeVideo',
                    event_action:'Complete'
                },
                PauseVideo: {
                    event: 'GAEvent.PausePlayYouTubeVideo',
                    event_action:'Pause'
                },
                StartingVideo: {
                    event: 'GAEvent.StartPlayYouTubeVideo',
                    event_action:'Starting'
                }
            }
        },
        ContactUsForm: {
            category:'ContactUsForm',
            actions: {
                contactUsSubmitHandler: {
                    event:'GAEvent.ContactUsSubmitHandler',
                    event_action:'ContactUs Submit'
                },
                contactUsSubmitSuccessfullyHandler: {
                    event:'GAEvent.ContactUsSubmitSuccessfullyHandler',
                    event_action:'Submit Successfully Contact Us Form'
                },
                contactUsSubmitErrorHandler: {
                    event:'GAEvent.ContactUsSubmitErrorHandler',
                    event_action:'Submit Error Contact Us Form'
                },
            }
        },
        ProductRegistration: {
            category:'ProductRegistration',
            actions: {
                productRegistrationHandler: {
                    event:'GAEvent.ProductRegistrationHandler',
                    event_action:'Click Register On Form'
                },
                productRegistrationSubmitErrorHandler: {
                    event:'GAEvent.ProductRegistrationSubmitErrorHandler',
                    event_action:'Submit Error On Form'
                },
                productRegistrationSubmitSuccessHandler: {
                    event:'GAEvent.ProductRegistrationSubmitSuccessHandler',
                    event_action:'Submit Successfully On Form'
                },
            }
        },
        Subscription: {
            category:'Subscription',
            actions: {
                subscribeFormSubmitHandler: {
                    event:'GAEvent.SubscribeFormSubmitHandler',
                    event_action:'Click Subscribe On Form'
                },
                subscribeFormSubmitSuccessHandler: {
                    event:'GAEvent.SubscribeFormSubmitSuccessHandler',
                    event_action:'Subscribe Successfully'
                },
            }
        },
        CommercialForm: {
            category:'CommercialForm',
            actions: {
                commercialSubmitFormHandler: {
                    event:'GAEvent.CommercialSubmitFormHandler',
                    event_action:'Click Submit On Commercial Form'
                },
                commercialSubmitFormSuccessHandler: {
                    event:'GAEvent.CommercialSubmitFormSuccessHandler',
                    event_action:'Submit Successfully Commercial Form'
                },
                commercialSubmitFormErrorHandler: {
                    event:'GAEvent.CommercialSubmitFormErrorHandler',
                    event_action:'Submit Error Commercial Form'
                },
            }
        },
        Wishlist: {
            category:'Wishlist',
            actions: {
                wishlistAddToHandler: {
                    event:'GAEvent.WishlistAddToHandler',
                    event_action:'Add product to Wishlist'
                },
                wishlistProductRemoveHandler: {
                    event:'GAEvent.WishlistProductRemoveHandler',
                    event_action:'Remove product from Wishlist'
                },
                wishlistEmailHandler: {
                    event:'GAEvent.WishlistEmailHandler',
                    event_action:'Click Email Wishlist'
                },
                wishlistDownloadHandler: {
                    event:'GAEvent.WishlistDownloadHandler',
                    event_action:'Click Download Wishlist'
                },
            }
        },
        Search: {
            category:'Search',
            actions: {
                searchViewAllResultsHandler: {
                    event:'GAEvent.SearchViewAllResultsHandler',
                    event_action:'View All Results'
                },
            }
        },
        StoreLocator: {
            category:'StoreLocator',
            actions: {
                storeLocatorSearchByAddressHandler: {
                    event:'GAEvent.StoreLocatorSearchByAddressHandler',
                    event_action:'Click Search By Address'
                },
                storeLocatorClickStoreNameHandler: {
                    event:'GAEvent.StoreLocatorClickStoreNameHandler',
                    event_action:'Click Store Name'
                },
                storeLocatorClickStorePhoneHandler: {
                    event:'GAEvent.StoreLocatorClickStorePhoneHandler',
                    event_action:'Click Store Phone Number'
                },
            }
        },
        ServiceCentres: {
            category:'ServiceCentres',
            actions: {
                serviceCentreSearchHandler: {
                    event:'GAEvent.ServiceCentreSearchHandler',
                    event_action:'Click Search Service Centres'
                },
            }
        },
        TimeOnPage: {
            category:'TimeOnPage',
            actions: {
                timeOnPageViewHandler: {
                    event:'GAEvent.TimeOnPageViewHandler',
                    event_action:'Time On Page View'
                },
            }
        },
    };

    window.niteco_gtm = window.niteco_gtm || {};
    window.niteco_gtm.configs = {
        pageRegistry: pageRegistry,        refLocatorMap: refLocatorMap,
        eventCategoryMapping: eventCategoryMapping
    };

    function script(url) {
        var s = document.createElement('script');
        s.type = 'text/javascript';
        s.async = true;
        s.defer = true;
        s.src = url;
        var x = document.getElementsByTagName('head')[0];
        x.appendChild(s);
    }

    (function () { script('https://elux-apac-dev.niteco.asia/gtm-scripts/anz/westinghouse/nz/dev/settings.dev.js?_t=' + new Date().getTime());
    })();

</script>