php -S 127.0.0.1:9090 -t public/  server.php

https://docs.google.com/spreadsheets/d/1yWVT4uBZUSsXIW4P1v1UN_XINFSTSuLY6ZI8A0jmtB8/edit#gid=874822776

TRANG LIEN HỆ: lien-he.shtml
https://phatgiaothanhhoa.com/lien-he.shtm

TRANG TIN: tin-tuc
https://phatgiaothanhhoa.com/tin-tuc

TRANG TIN CHI TIẾT {title}.html
https://phatgiaothanhhoa.com/lanh-dao-cac-ban-nganh-tinh-thanh-hoa-chuc-mung-ban-tri-su-phat-giao-tinh-nhan-dai-le-vu-lan-nam-2020.html

* RUN SQL:
ALTER TABLE `news` CHANGE `title_seo` `title_seo` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'JSON data', CHANGE `description_seo` `description_seo` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'JSON data', CHANGE `keyword_seo` `keyword_seo` VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT 'JSON data';

ALTER TABLE `categories` ADD `left_menu` SMALLINT(2) NULL DEFAULT '0' AFTER `slugify`;

ALTER TABLE `news` ADD `viewed` INT(11) NULL DEFAULT '1' AFTER `slugify`;

DROP TABLE IF EXISTS `vieweds`;
CREATE TABLE `vieweds` (
  `id` int(11) NOT NULL,
  `news_id` int(11) NOT NULL DEFAULT '0',
  `counter` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

Mysql:
DB: phatg626_pgth
U: phatg626_pgth
Pass: hnC3flnH@aD=