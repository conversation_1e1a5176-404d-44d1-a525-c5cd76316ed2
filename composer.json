{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.1.3", "ext-curl": "*", "ext-json": "*", "ckfinder/ckfinder-laravel-package": "v3.5.1.6", "doctrine/dbal": "^2.12", "fideloper/proxy": "^4.0", "fzaninotto/faker": "^1.8", "intervention/image": "2.5.1", "laravel/framework": "5.8.*", "laravel/tinker": "^1.0", "maatwebsite/excel": "^3.1", "paquettg/php-html-parser": "3.1.0", "phpseclib/phpseclib": "~1.0", "unisharp/laravel-filemanager": "~1.8"}, "require-dev": {"beyondcode/laravel-dump-server": "^1.0", "filp/whoops": "^2.0", "mockery/mockery": "^1.0", "nunomaduro/collision": "^3.0", "phpunit/phpunit": "^7.5", "xethron/migrations-generator": "^2.0"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/"}, "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}}