@extends('layouts.admin.admin')

@section('content')
    <style>
        .popover-body {
            background-color: antiquewhite;
            font-weight: bold;
        }
    </style>
    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item" href="/">Trang chủ</li>

            <li class="breadcrumb-item active font-weight-bold">DANH MỤC TIN TỨC</li>

        </ol>

        <div class="container-fluid">
            <div class="animated fadeIn">
                <div class="row">
                    <div class="col-sm-12 col-xl-6">
                        <div class="card">
                            <div class="card-header bg-primary">
                                <i class="fa fa-align-justify"></i> Danh mục cấp 1
                            </div>
                            <div class="card-body" style="max-height: 460px; overflow-y: scroll">
                                <ul class="list-group">
                                    @if(!empty($categories))
                                        @php $count = 0 @endphp
                                        @foreach($categories as $category)
                                            @if($category->category_level == 1)
                                            <li id="cat-item-{{ $category->id }}" onclick="Category.children({{ $category->id }}, 2)" style="cursor: pointer" class="list-group-item d-flex list-group-item-action justify-content-between align-items-center">
                                                {{ ($category->category_title) }}
                                                <span onclick="return Category.statusChange({{ $category->id }}, this)" title="Nhấn chuột để Bật hoặc Tắt danh mục" class="badge badge-primary badge-pill {{ $category->status == 1 ? '':'bg-danger' }}">{{ !empty($category->children) ? count($category->children) : 0 }}</span>
                                            </li>
                                            @endif
                                        @endforeach
                                    @else
                                        <li class="list-group-item">Chưa có danh mục nào</li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-sm-12 col-xl-6">
                        <div class="card">
                            <div class="card-header bg-primary">
                                <i class="fa fa-align-justify"></i> Danh mục cấp 2
                            </div>
                            <div class="card-body" style="max-height: 460px; overflow-y: scroll">
                                <ul class="list-group" id="category_level_2">
                                    <li class="list-group-item">Vui lòng chọn một danh mục cấp 1</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row" id="field_data">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header">
                                <strong id="action-title">Thêm mới Danh mục</strong> &nbsp;
                                <button style="margin-bottom: -6px" disabled name="btn-new" id="btn-new" onclick="Category.new()" type="button" class="btn btn-success float-right"><strong>Thêm mới</strong></button>
                            </div>
                            <div class="card-body">
                                <form name="form-category" id="form-category" method="post" enctype="multipart/form-data">
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="name">Tên danh mục</label>
                                                <input type="text" required class="form-control" id="category_title" name="category_title" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <div class="form-group">
                                                    <label for="name">Mô tả ngắn</label>
                                                    <textarea class="form-control" id="category_description" name="category_description" placeholder="Mô tả ngắn"></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="ccnumber">Title SEO</label>
                                                <input type="text" class="form-control" id="category_title_seo" name="category_title_seo" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="ccnumber">Description SEO</label>
                                                <input type="text" class="form-control" id="category_description_seo" name="category_description_seo" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="ccnumber">Từ khoá SEO</label>
                                                <input type="text" class="form-control" id="category_keyword_seo" name="category_keyword_seo" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="ccnumber">Đường dẫn trực tiếp (ưu tiên nếu có)</label>
                                                <input type="text" class="form-control" id="redirect_link" name="redirect_link" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="ccmonth">Danh mục cha</label>
                                                <select class="form-control" id="parent_id" name="parent_id">
                                                    <option value="0">Là Danh mục cấp một</option>
                                                    @if($categories)
                                                        @foreach($categories as $category)
                                                            <option value="{{ $category->id }}">
                                                                {{ ($category->category_title) }}
                                                            </option>

                                                            @if(!empty($category->children))
                                                                @foreach($category->children as $children)
                                                                    <option value="{{ $children->id }}">
                                                                        -- {{ ($children->category_title) }}
                                                                    </option>
                                                                @endforeach
                                                            @endif
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">

                                        <div class="form-group col-sm-6">
                                            <label for="ccmonth">Hiển thị Danh mục trái</label>
                                            <select class="form-control" id="left_menu" name="left_menu">
                                                {!! \App\Helpers\Constant::optionLeftMenu() !!}
                                            </select>
                                        </div>

                                        <div class="form-group col-sm-3">
                                            <label for="ccmonth">Trạng thái</label>
                                            <select class="form-control" id="status" name="status">
                                                {!! \App\Helpers\Constant::optionOnOff() !!}
                                            </select>
                                        </div>

                                        <div class="form-group col-sm-3">
                                            <label for="ccmonth">Thứ tự sắp xếp</label>
                                            <input type="text" class="form-control" id="sort" name="sort" placeholder="">
                                        </div>

                                    </div>
                                    <!--/.row-->

                                    <div class="row">

                                        <div class="form-group col-sm-4">
                                            <label for="ccyear">Hiển thị Menu Chính</label>
                                            <select class="form-control" id="menu_top" name="menu_top">
                                                {!! \App\Helpers\Constant::optionYesNo() !!}
                                            </select>
                                        </div>

                                        <div class="form-group col-sm-4">
                                            <label for="ccyear">Hiển thị Trang chủ</label>
                                            <select class="form-control" id="hot" name="hot">
                                                {!! \App\Helpers\Constant::optionYesNo() !!}
                                            </select>
                                        </div>

                                        <div class="form-group col-sm-4">
                                            <label for="ccyear">Chọn Ảnh</label>
                                            <button type="button" class="form-control btn btn-warning" name="select-photo" id="select-photo" onclick="Category.selectFileWithCKFinder('photo')">Ảnh từ Thư viện</button>
                                            <input type="hidden" name="photo" id="photo" value="" />
                                        </div>

                                        <div class="form-group col-sm-4 d-none">
                                            <label for="ccyear">Tải Ảnh từ máy tính</label>
                                            <input type="file" class="form-control" id="upload-photo" name="upload_photo">
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="form-group col-sm-12">
                                            <img class="float-right" id="photo_show" src="/images/news_default.png" style="max-width: 150px;max-height: 300px;">
                                        </div>
                                    </div>

                                    <input type="hidden" value="0" name="id" id="id" />
                                </form>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary" onclick="return Category.save()">Cập nhật</button>
                                    <button type="button" class="btn btn-secondary" onclick="Category.reset()">Làm lại</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <script src="{{ asset('/js/ckeditor/ckeditor.js') }}"></script>
    <script type="text/javascript" src="/js/ckfinder/ckfinder.js"></script>
    <script>
        CKFinder.config( { language: 'vi', connectorPath: '/ckfinder/connector' } );

        $(function () {
            Category.init();

        })

        const Category = {
            baseUrl: '{{ url('/') }}',
            urlSave: '{{ route('admin.category_update') }}',
            urlChildren: '{{ route('admin.category_get_children') }}',
            urlDetail: '{{ route('admin.category_detail') }}',
            urlUpdate: '{{ route('admin.category_update_by_field') }}',
            init: function () {
                $('[data-toggle="popover"]').popover();
                $("textarea").each(function () {
                    let id = $(this).attr('id');
                    let editor = CKEDITOR.replace(id, {
                        language: 'vi'
                    });
                    CKFinder.setupCKEditor( editor );
                });
            },
            new: function() {
                Category.reset();
                $('#btn-new').prop('disabled', true);
                $('#action-title').html('Thêm mới danh mục');
                $('#category_title').focus();
                $('html, body').animate({
                    scrollTop: $("#field_data").offset().top - 50
                }, 1000);
            },
            statusChange: function (id, _this) {
                SystemScript.postAjax(Category.urlUpdate, {id: id, field: 'status'}, function (res) {
                    console.log(res);
                    if(res.code == 0) {
                        if(_this) {
                            if(res.data.status == 0) {
                                $(_this).addClass('bg-danger');
                            } else {
                                $(_this).removeClass('bg-danger');
                            }
                        }
                    } else {
                        alert(res.message);
                    }
                });
                return false;
            },
            children: function (id, level) {
                SystemScript.getAjax(Category.urlChildren, {id, level}, function (res) {
                    if(res.code == 0) {
                        $('#category_level_' + level).html(res.data.htmlData);
                        var detailData = res.data.detailData;
                        if(detailData) {
                            for(var key in detailData) {
                                $('#' + key).val(detailData[key]);

                                if(key === 'photo') {
                                    $('#photo_show').attr('src', detailData[key]);
                                }

                                if(key === 'category_description') {
                                    CKEDITOR.instances['category_description'].setData(detailData[key])
                                }
                            }
                        }

                        $('#action-title').html('Chỉnh sửa danh mục');
                        $('#category_title').focus();
                        $('html, body').animate({
                            //scrollTop: $("#field_data").offset().top - 50
                        }, 1000);
                    } else {
                        alert(res.message);
                    }
                });
                $('#btn-new').prop('disabled', false);
            },
            save: function () {
                var category_title = $('#category_title').val();
                //var uploadPhoto = $('#upload-photo').get(0).files.length;

                if(!category_title || $.trim(category_title) === "") {
                    $('#category_title').focus();
                    return false;
                }

                let formData = new FormData($('#form-category')[0]);
                formData.append('category_description', CKEDITOR.instances['category_description'].getData());

                SystemScript.postAjaxFormData(Category.urlSave, formData, function (res) {
                    if(res.code === 0) {
                        alert('Cập nhật thành công');
                        var id = $("#id").val();
                        $('#cat-item-' + id).html($('#category_title').val());
                    } else {
                        alert(res.message);
                    }
                });
            },
            selectPhoto: function() {

            },
            reset: function () {
                jQuery('#upload-photo').val("");
                jQuery("input[type='text']").val("");
                jQuery("input[type='hidden']").val("");
                jQuery("input[type='number']").val("");
                $('#btn-new').prop('disabled', false);
            },
            selectFileWithCKFinder: function ( elementId ) {
                CKFinder.popup({
                    chooseFiles: true,
                    width: 800,
                    height: 600,
                    language: 'vi',
                    onInit: function( finder ) {
                        finder.on( 'files:choose', function( evt ) {
                            var file = evt.data.files.first();
                            var output = document.getElementById( elementId );
                            output.value = file.getUrl().replace(Category.baseUrl, '');
                            $('#photo_show').attr('src', file.getUrl());
                        } );

                        finder.on( 'file:choose:resizedImage', function( evt ) {
                            var output = document.getElementById( elementId );
                            $('#photo_show').attr('src', evt.data.resizedUrl);
                            output.value = evt.data.resizedUrl.replace(Category.baseUrl, '');
                        } );
                    }
                });
            }
        };
    </script>

@endsection