@extends('layouts.admin.admin')

@section('content')

    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li> Dashboard </li>
        </ol>

        <div class="container-fluid">

            <div class="animated fadeIn">
                <div class="row">

                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white bg-danger">
                            <div class="card-body pb-0 summary-box">
                                <h4 class="mb-0 summary-number">{{ App\Models\News::count() }}</h4>
                                <p>Tổng số Bài viết</p>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->

                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white bg-warning">
                            <div class="card-body pb-0 summary-box">
                                <h4 class="mb-0 summary-number">{{ number_format(App\Models\Category::count()) }}</h4>
                                <p>Tổng s<PERSON> mục</p>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->

                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white bg-success">
                            <div class="card-body pb-0 summary-box">
                                <h4 class="mb-0 summary-number">{{ number_format(App\Models\User::count()) }}</h4>
                                <p>Tổng số thành viên</p>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->

                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white bg-primary">
                            <div class="card-body pb-0 summary-box">
                                <h4 class="mb-0 summary-number">{{ number_format(App\Models\Page::count()) }}</h4>
                                <p>Tổng số Trang tĩnh</p>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->
                </div>
                <!--/.row-->

                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                Hoá đơn được kiểm tra hôm nay, Tổng <strong>{{number_format($orders->count())}}</strong>.
                                Trong đó <strong style="color: green">ONLINE: {{number_format($orders->count() - $totalOffline) }}</strong>;
                                <strong style="color: blue">OFFLINE: {{number_format($totalOffline)}}</strong>;
                                <strong style="color: orangered">Lỗi: {{number_format($totalError)}}</strong>;
                            </div>
                            <div class="card-body">

                                <br>
                                <table class="table table-responsive-sm table-bordered table-striped table-sm">
                                    <thead class="thead-light">
                                    <tr>
                                        <th>No</th>
                                        <th>Full name</th>
                                        <th>Member code</th>
                                        <th>Identity</th>
                                        <th>Product Name</th>
                                        <th>Order time</th>
                                        <th>Cost</th>
                                        <th>PP</th>
                                        <th>Quantity</th>
                                        <th>Order Type</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @if($orders)
                                        @foreach($orders as $idx => $order)
                                            <tr style="{{ $order->status != 1 ? 'background: chocolate' : '' }}">
                                                <td title="{{ $order->created_at }}">{{ $idx + 1 }}</td>
                                                <td>{{ $order->full_name }}</td>
                                                <td>{{ $order->member_code }}</td>
                                                <td>{{ $order->order_code }}</td>
                                                <td>{{ $order->product_name }}</td>
                                                <td>{{ $order->order_time }}</td>
                                                <td>{{ $order->cost }}</td>
                                                <td>{{ $order->pp }}</td>
                                                <td>{{ $order->quantity }}</td>
                                                <td>
                                                    @if($order->order_type == \App\Helpers\Constant::ONLINE)
                                                        <strong style="color: green">{{ $order->order_type }}</strong>
                                                    @elseif($order->order_type == \App\Helpers\Constant::OFFLINE)
                                                        <strong style="color: blue">{{ $order->order_type }}</strong>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->
                </div>
                <!--/.row-->
            </div>

        </div>
        <!-- /.conainer-fluid -->
    </main>


@endsection
