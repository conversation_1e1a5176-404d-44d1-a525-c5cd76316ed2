@extends('layouts.admin.admin')

@section('content')

    <style>
        .popover-body {
            background-color: antiquewhite;
            font-weight: bold;
        }

        .ui-autocomplete {
            max-width: 600px;
            max-height: 300px !important;
            overflow-y: scroll !important;
        }
    </style>
    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item" href="/">Trang chủ</li>

            <li class="breadcrumb-item active">TIN TỨC</li>

        </ol>

        <div class="container-fluid">
            <div class="animated fadeIn">

                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header bg-primary">
                                <strong>{{ $news ? 'Sửa bài viết' : 'Thêm mới bì viết' }}</strong>
                            </div>
                            <div class="card-body">
                                @include('layouts.admin.partial.flash_error', [])

                                <form action="{{ route('admin.news_update') }}" name="form-news" id="form-news" method="post" enctype="multipart/form-data">
                                    @csrf
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="name">Tiêu đề</label>
                                                <input type="text" value="{{ (@$news->title) }}" required class="form-control" id="title" name="title" placeholder="">
                                            </div>

                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="name">Mô tả ngắn</label>
                                                <textarea class="form-control" id="description" name="description" placeholder="">{{ (@$news->description) }}</textarea>
                                            </div>

                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="name">Nội dung</label>
                                                <textarea class="form-control" id="content" name="content" placeholder="">
                                                    {{ (@$news->content) }}
                                                </textarea>
                                            </div>

                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="ccnumber">Title SEO</label>
                                                <input value="{{ @$news->title_seo }}" type="text" class="form-control" id="title_seo" name="title_seo" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="ccnumber">Description SEO</label>
                                                <input value="{{ @$news->description_seo }}" type="text" class="form-control" id="description_seo" name="description_seo" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group">
                                                <label for="ccnumber">Từ khoá SEO</label>
                                                <input value="{{ @$news->keyword_seo }}" type="text" class="form-control" id="keyword_seo" name="keyword_seo" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label for="ccnumber">Ngày public</label>
                                                <input value="{{ \App\Helpers\Utils::formatDateToCalendar(@$news->date_public) }}" type="text" class="form-control datepicker" id="date_public" name="date_public" placeholder="">
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="form-group">
                                                <label for="ccnumber">Ngày ẩn tin</label>
                                                <input value="{{ \App\Helpers\Utils::formatDateToCalendar(@$news->date_end) }}" type="text" class="form-control datepicker" id="date_end" name="date_end" placeholder="">
                                            </div>
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="form-group col-sm-6">
                                            <label for="ccmonth">Chọn Danh mục</label>
                                            <select class="form-control" id="category_id" name="category_id">
                                                <option value="0">Không thuộc Danh mục</option>
                                                {!! \App\Helpers\Constant::optionCategories($categories, @$news->category_id) !!}
                                            </select>
                                        </div>

                                        <div class="form-group col-sm-3">
                                            <label for="ccmonth">Trạng thái</label>
                                            <select class="form-control" id="status" name="status">
                                                {!! \App\Helpers\Constant::optionOnOff(@$news->status) !!}
                                            </select>
                                        </div>

                                        <div class="col-sm-3">
                                            <div class="form-group">
                                                <label for="ccnumber">Tác giả</label>
                                                <input type="text" class="form-control" id="author" name="author" value="{{ isset($news->author) ? $news->author : 'Ban biên tập nhà chùa' }}" placeholder="">
                                            </div>
                                        </div>

                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="form-group col-sm-6">
                                            <label for="ccmonth">Tin nổi bật</label>
                                            <select class="form-control" id="is_hot" name="is_hot">
                                                {!! \App\Helpers\Constant::optionYesNo(@$news->is_hot) !!}
                                            </select>
                                        </div>

                                        <div class="form-group col-sm-6">
                                            <label for="ccmonth">Tin tiêu điểm</label>
                                            <select class="form-control" id="is_focus" name="is_focus">
                                                {!! \App\Helpers\Constant::optionYesNo(@$news->is_focus) !!}
                                            </select>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="form-group col-sm-6">
                                            <label for="ccmonth">Danh mục - Tags</label>
                                            <input type="text" class="form-control" id="tags" name="tags" value="{{ @$news->tags }}" placeholder="">
                                        </div>

                                        <div class="form-group col-sm-6">
                                            <label for="ccyear">Chọn Ảnh</label>
                                            <button type="button" class="form-control btn btn-warning" name="select-photo" id="select-photo" onclick="News.selectFileWithCKFinder('photo')">Ảnh từ Thư viện</button>
                                            <input type="hidden" name="photo" id="photo" value="{{ @$news->photo }}" />
                                        </div>
                                    </div>
                                    <!--/.row-->

                                    <div class="row">
                                        <div class="form-group col-sm-12">
                                            <img class="float-right" id="photo_show" src="{{ !empty($news) && $news->photo ? $news->photo : '/images/news_default.png' }}" style="max-width: 300px;max-height: 300px;">
                                        </div>
                                    </div>

                                    <input type="hidden" value="{{ @$news->id }}" name="id" id="id" />
                                </form>

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary" onclick="return News.save()">Cập nhật</button>
                                    <button type="button" class="btn btn-secondary" onclick="News.reset()">Làm lại</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script src="{{ asset('/js/ckeditor/ckeditor.js') }}"></script>
    <script type="text/javascript" src="/js/ckfinder/ckfinder.js"></script>

    <script>
        CKFinder.config( { connectorPath: '/ckfinder/connector', language: 'vi' } );
        $(function () {
            News.init();
        })

        const News = {
            baseUrl: '{{ url('/') }}',
            urlSave: '{{ route('admin.news_update') }}',
            init: function () {
                $("textarea").each(function () {
                    let id = $(this).attr('id');
                    let option = id === 'content' ? {
                            width: '100%',
                            height: 700,
                            language: 'vi'
                        } : {language: 'vi'};
                    let editor = CKEDITOR.replace(id, option);
                    CKFinder.setupCKEditor( editor );
                });

                $('#tags').tagsInput({
                    placeholder: 'Gõ một ký tự để tìm...',
                    'autocomplete': {
                        source: {!! json_encode($categoryTitleAll) !!}
                    }
                });
            },
            new: function() {
                News.reset();
                $('#btn-new').prop('disabled', true);
            },
            save: function () {

                var title = $('#title').val();
                var photo = $('#photo').val();
                //var uploadPhoto = $('#upload-photo').get(0).files.length;

                if(!title || $.trim(title) == "") {
                    $('#title').focus();
                    return false;
                }

                if(!photo) {
                    $.alert({
                        title: 'Có Lỗi!',
                        content: 'Vui lòng chọn ảnh hoặc upload một ảnh mới cho tim tức',
                    })
                    return false;
                }
                $('#form-news').submit();
            },
            selectPhoto: function() {

            },
            reset: function () {
                jQuery('#upload-photo').val("");
                jQuery("input[type='text']").val("");
                jQuery("input[type='hidden']").val("");
                jQuery("input[type='number']").val("");
                $('#btn-new').prop('disabled', false);
            },
            selectFileWithCKFinder: function ( elementId ) {
                CKFinder.popup({
                    chooseFiles: true,
                    width: 800,
                    height: 600,
                    language: 'vi',
                    onInit: function( finder ) {
                        finder.on( 'files:choose', function( evt ) {
                            var file = evt.data.files.first();
                            var output = document.getElementById( elementId );
                            output.value = file.getUrl().replace(News.baseUrl, '');
                            $('#photo_show').attr('src', file.getUrl());
                        } );

                        finder.on( 'file:choose:resizedImage', function( evt ) {
                            var output = document.getElementById( elementId );
                            $('#photo_show').attr('src', evt.data.resizedUrl);
                            output.value = evt.data.resizedUrl.replace(News.baseUrl, '');
                        } );
                    }
                });
            }
        };
    </script>

@endsection