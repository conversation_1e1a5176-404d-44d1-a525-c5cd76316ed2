
@extends('layouts.admin.admin')

@section('content')

    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Trang chủ</li>

            <li class="breadcrumb-item active">Bài viết</li>

        </ol>

        <div class="container-fluid">
            <div class="animated fadeIn">
                <div class="card">
                    <div class="card-header bg-primary">
                        <strong>Tìm kiếm Bài viết</strong>
                    </div>
                    <div class="card-body">
                        <form id="search-news" name="search-news" action="" method="get" class="form-horizontal">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group row">
                                        <label class="col-md-3 col-form-label" for="hf-email">Từ cần tìm</label>
                                        <div class="col-md-9">
                                            <input type="text" id="title" value="{{ $request->get('title') }}" name="title" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-md-3 col-form-label" for="hf-password">Danh mục</label>
                                        <div class="col-md-9">
                                            <select class="form-control" id="category_id" name="category_id">
                                                <option value="-1">Tất cả</option>
                                                <option value="0">Không thuộc Danh mục</option>
                                                @php
                                                    $category_id = $request->get('category_id', 0);
                                                @endphp
                                                @if($categories)
                                                    @foreach($categories as $category)
                                                        <option {{ $category_id == $category->id ? 'selected' : '' }} value="{{ $category->id }}">
                                                            {{ ($category->category_title) }}
                                                        </option>

                                                        @if(!empty($category->children))
                                                            @foreach($category->children as $category2)
                                                                <option {{ $category_id == $category2->id ? 'selected' : '' }} value="{{ $category2->id }}">
                                                                    -- {{ ($category2->category_title) }}
                                                                </option>
                                                                @if(!empty($category2->children))
                                                                    @foreach($category2->children as $category3)
                                                                        <option {{ $category_id == $category3->id ? 'selected' : '' }} value="{{ $category3->id }}">
                                                                            --- {{ ($category3->category_title) }}
                                                                        </option>
                                                                    @endforeach
                                                                @endif
                                                            @endforeach
                                                        @endif
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-sm-6">
                                    <div class="form-group row">
                                        <label class="col-md-3 col-form-label" for="hf-email">Ngày đăng</label>
                                        <div class="col-md-9">
                                            <input type="text" autocomplete="off" value="{{ $request->get('created_at') }}" id="created_at" name="created_at" class="datetimefilter form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-md-3 col-form-label" for="hf-email">Trạng thái</label>
                                        <div class="col-md-9">
                                            <select class="form-control" id="status" name="status">
                                                <option value="-1" {{ -1 == $request->get('status', 1) ? 'selected': '' }}>Tất cả</option>
                                                {!! \App\Helpers\Constant::optionOnOff($request->get('status', -1)) !!}

                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer">
                        <button type="submit" onclick=" return NewsList.submit()" class="btn btn-sm btn-primary"><i class="fa fa-dot-circle-o"></i> Tìm kiếm</button>
                        <button type="reset" onclick="NewsList.reset()" class="btn btn-sm btn-danger"><i class="fa fa-ban"></i> Làm lại</button>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fa fa-edit"></i> Danh sách Bài viết (Tổng số: {{ !empty($news) ? number_format($news->total()) : 0 }})
                        <div class="card-actions">
                            <a href="https://datatables.net">
                                <small class="text-muted">docs</small>
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-responsive-sm table-bordered table-striped table-sm">
                            <thead class="thead-light">
                            <tr>
                                <th>#</th>
                                <th>Ảnh</th>
                                <th>Tiêu đề</th>
                                <th>Danh mục</th>
                                <th>Ngày đăng</th>
                                <th>Nổi bật</th>
                                <th>Tiêu điểm</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                            </thead>
                            <tbody>
                            @if($news)
                                @foreach($news as $idx => $row)
                                    <tr>
                                        <td>{{ ($request->input('page', 1) - 1) * \App\Helpers\Constant::ITEM_PER_PAGE + ($idx + 1) }}</td>
                                        <td align="center">
                                            <p style="min-width: 120px">
                                                @if($row->photo)
                                                <img style="width: 145px" src="{{ \App\Helpers\Utils::formatPathFile($row->photo) }}" />
                                                @endif
                                            </p>
                                        </td>
                                        <td align="left" style="word-wrap: break-word;max-width: 500px;">
                                            <a target="_blank" href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($row->title)]) }}" title="Mở tin">{!! ($row->title) !!}</a>
                                            <div style="margin-top: 15px;"><span class="fa fa-eye small"></span> <span>{{ $row->viewed }}</span></div>
                                        </td>
                                        <td>
                                            {{ $row->category_id && !empty($mapsCategory[$row->category_id]) ? ($mapsCategory[$row->category_id]->category_title) : 'Không có danh mục' }}

                                            @if($row->tags)
                                            <div style="padding-top: 20px">
                                                <strong>Tags</strong>: <i>{{ str_replace(',',', ', $row->tags) }}</i>
                                            </div>
                                            @endif
                                        </td>
                                        <td align="center">
                                            <p>Ngày đăng: <strong>{{ $row->created_at }}</strong></p>
                                            @if($row->date_public)
                                            <p>Ngày bật: <strong>{{ $row->date_public }}</strong></p>
                                            @endif
                                            @if($row->date_end)
                                            <p>Ngày kết thúc: <strong>{{ $row->date_end }}</strong></p>
                                            @endif
                                        </td>

                                        <td align="center" style="padding-bottom: 15px;text-align: center;font-weight: bold;">

                                            <div class="form-check checkbox" style="margin-bottom: 15px;">
                                                <input class="form-check-input" {{ $row->is_hot == 1 ? 'checked' : '' }} onchange="NewsList.statusChange({{ $row->id }}, 'is_hot')" type="checkbox" value="" id="news-is_hot-{{ $row->id }}">
                                            </div>
                                        </td>

                                        <td align="center" style="padding-bottom: 15px;text-align: center;font-weight: bold;">

                                            <div class="form-check checkbox" style="margin-bottom: 15px;">
                                                <input class="form-check-input" {{ $row->is_focus == 1 ? 'checked' : '' }} onchange="NewsList.statusChange({{ $row->id }}, 'is_focus')" type="checkbox" value="" id="news-is_focus-{{ $row->id }}">
                                            </div>
                                        </td>

                                        <td align="center" style="padding-bottom: 15px;text-align: center;font-weight: bold;">

                                            <div class="form-check checkbox" style="margin-bottom: 15px;">
                                                <input class="form-check-input" {{ $row->status == 1 ? 'checked' : '' }} onchange="NewsList.statusChange({{ $row->id }}, 'status')" type="checkbox" value="" id="news-status-{{ $row->id }}">
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.news_edit', ['id' => $row->id]) }}">Sửa</a>
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                            </tbody>
                        </table>
                        {!! $news->appends($request->input())->render();  !!}
                    </div>
                </div>
            </div>

        </div>
        <!-- /.conainer-fluid -->
    </main>

    <script>
        $(function () {
            NewsList.init();
        });

        const NewsList = {
            urlUpdate: '{{ route('admin.news_update_by_field') }}',
            init: function () {

            },
            submit: function () {
                $('#search-news').submit();
            },
            statusChange: function (id, field) {
                SystemScript.postAjax(NewsList.urlUpdate, {id: id, field: field}, function (res) {
                    console.log(res);
                    if(res.code == 0) {
                        $('#news-status-label-' + id).prop('checked', res.data.status);
                    } else {
                        alert(res.message);
                    }
                });
            },
            reset: function () {
                window.location.href = '{{ route('admin.news') }}';
            }
        }
    </script>
@endsection
