@extends('layouts.admin.admin')

@section('content')

    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Admin</li>
            <li class="breadcrumb-item"><a href="#">Quản lý Nhân viên</a></li>
            <li class="breadcrumb-item active">Cập nhật</li>
        </ol>

        <div class="container-fluid">

            <div class="animated fadeIn">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header">
                                <i class="fa fa-edits"></i>
                                <div class="card-actions">

                                </div>
                            </div>
                            <div class="card-body collapse show" id="collapseExample">
                                <form class="form-horizontal" method="post" id="updateForm">
                                    <input type="hidden" value="{{ $user->id }}" name="_id" id="_id" />
                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Họ tên</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="name" name="name" class="form-control" value="{{ $user->name }}" type="text">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Số điện thoại</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="phone" name="phone" class="form-control" value="{{ $user->phone }}" type="text">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Email</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="email" name="email" value="{{ $user->email }}" class="form-control" type="text">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Mật khẩu</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="password" type="password" name="password" class="form-control" type="text">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Nhập lại Mật khẩu</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="repassword" type="password" name="repassword" class="form-control" type="text">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Trạng thái</label>
                                        <div class="controls">
                                            <select class="form-control" id="is_active" name="is_active">
                                                <option {{ $user->is_active == 1 ? 'selected' : '' }} value="1">Hoạt động</option>
                                                <option {{ $user->is_active == 0 ? 'selected' : '' }} value="0">Ngừng Hoạt động</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Quyền</label>
                                        <div class="controls">
                                            <select class="form-control" id="is_role" name="is_role">
                                                <option {{ $user->is_role == 1 ? 'selected' : '' }} value="1">Nhân viên</option>
                                                <option {{ $user->is_role == 2 ? 'selected' : '' }} value="2">Admin</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">Lưu thay đổi</button>
                                    </div>
                                    @csrf
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <!--/.row-->
            </div>

        </div>
        <!-- /.conainer-fluid -->
    </main>

    <script>
        $(function () {
            $.validator.setDefaults( {
                submitHandler: function () {
                    if($('#password').val() != $('#repassword').val()) {
                        $.alert({
                            title: 'Thông báo!',
                            content: 'Nhập hai mật khâủ phải giống nhau',
                        });
                        return  false;
                    }
                    return true;
                }
            });

            $('#updateForm').validate({
                rules: {
                    name: {
                        required: true,
                        minlength: 5
                    },
                    phone: {
                        required: true,
                        minlength: 10,
                        number:true
                    },
                    email: {
                        required: true,
                        email:true
                    }
                },
                messages: {
                    name: 'Vui lòng nhập Họ tên',
                    phone: 'Vui lòng nhập số điện thoại để liên lạc',
                    email: 'Vui lòng nhập email để đăng nhập',
                    password: 'Vui lòng nhập mật khẩu để đăng nhập',
                    repassword: 'Vui lòng nhập xác nhận mật khẩu',
                },
                errorElement: 'em',
                errorPlacement: function ( error, element ) {
                    error.addClass( 'invalid-feedback' );
                    if ( element.prop( 'type' ) === 'checkbox' ) {
                        error.insertAfter( element.parent( 'label' ) );
                    } else {
                        error.insertAfter( element );
                    }
                },
                highlight: function ( element, errorClass, validClass ) {
                    $( element ).addClass( 'is-invalid' ).removeClass( 'is-valid' );
                },
                unhighlight: function (element, errorClass, validClass) {
                    $( element ).addClass( 'is-valid' ).removeClass( 'is-invalid' );
                }
            });
        });
    </script>
@endsection
