@extends('layouts.admin.admin')

@section('content')
    <style>
        .col-form-label {
            /*text-align: right;*/
        }
    </style>
    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Home</li>

            <li class="breadcrumb-item active"><PERSON>h sách người dùng</li>

        </ol>

        <div class="container-fluid">
            <div class="animated fadeIn">
                <div class="card">
                    <div class="card-header">
                        <strong>Tìm kiếm</strong>
                    </div>
                    <div class="card-body">
                        <form id="search-user" action="" method="get" class="form-horizontal">
                            <div class="row">
                                <div class="form-group row col-md-4">
                                    <label class="col-md-4 col-form-label" for="hf-email"><PERSON>ố điện thoại</label>
                                    <div class="col-md-8">
                                        <input type="text" id="search-phone" value="{{ $request->get('phone') }}" name="phone" class="form-control" placeholder="">
                                    </div>
                                </div>
                                <div class="form-group row col-md-4">
                                    <label class="col-md-4 col-form-label" for="hf-password">Họ tên</label>
                                    <div class="col-md-8">
                                        <input type="text" id="search-name" value="{{ $request->get('full_name') }}" name="name" class="form-control" placeholder="">
                                    </div>
                                </div>
                                <div class="form-group row col-md-4">
                                    <label class="col-md-4 col-form-label" for="hf-email">Địa chỉ Email</label>
                                    <div class="col-md-8">
                                        <input type="text" id="search-email" name="email" value="{{ $request->get('email') }}" class="form-control" placeholder=" ">
                                    </div>
                                </div>

                            </div>
                        </form>
                    </div>
                    <div class="card-footer">
                        <button type="submit" onclick="$('#search-user').submit()" class="btn btn-sm btn-primary"><i class="fa fa-dot-circle-o"></i> Tìm</button>
                        <button type="reset" onclick="window.location = '/admin/user'" class="btn btn-sm btn-danger"><i class="fa fa-ban"></i> Là lại</button>
                        @if(\Illuminate\Support\Facades\Auth::user()->is_role == 2)
                            <button type="reset" class="btn btn-sm btn-success"  data-toggle="modal" data-target="#add_new_modal"><i class="fa fa-plus"></i> Thêm người dùng</button>
                        @endif
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fa fa-edit"></i> Danh sách
                        <div class="card-actions">
                            <a href="https://datatables.net">
                                <small class="text-muted">docs</small>
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-responsive-sm table-hover table-outline mb-0">
                            <thead class="thead-light">
                            <tr>
                                <th class="text-center"><i class="icon-people"></i></th>
                                <th>Họ tên</th>
                                <th class="text-center">Số điện thoại</th>
                                <th class="text-center">Địa chỉ Email</th>
                                <th style="text-align: center">Trạng thái</th>
                                <th>Thao tác</th>

                            </tr>
                            </thead>
                            <tbody>
                            @foreach($users as $user)
                            <tr>
                                <td class="text-center">
                                    <div class="avatar">
                                        <img src="{{ $user->avatar ? $user->avatar : '/images/avatar_default.png' }}" class="img-avatar" alt="<EMAIL>">
                                        {{--<span class="avatar-status badge-success"></span>--}}
                                    </div>
                                </td>
                                <td>
                                    <div>{{ $user->full_name }}</div>
                                    <div class="small text-muted">
                                        Created: {{ $user->created_at }}
                                    </div>
                                </td>

                                <td>
                                    {{ $user->phone }}
                                </td>
                                <td class="text-center">
                                    {{ $user->email }}
                                </td>
                                <td style="text-align: center">
                                    <div id="row_status_{{ $user->id }}">{{ $user->is_active == 1 ? 'Active' : 'InActive' }}</div>
                                    @if(\Illuminate\Support\Facades\Auth::user()->is_role == 2)
                                        <input class="row_status" onchange="User.updateStatus('{{ $user->id }}')" {{ $user->is_active == 1 ? 'checked' : '' }} type="checkbox" />
                                    @endif
                                </td>

                                <td><a href="#" onclick="User.edit(this)"
                                       full_name="{{ $user->full_name }}"
                                       user_name="{{ $user->user_name }}"
                                       phone="{{ $user->phone }}"
                                       email="{{ $user->email }}"
                                       is_role="{{ $user->is_role }}"
                                       id="{{ $user->id }}"
                                       is_active="{{ $user->is_active }}"
                                       data-toggle="modal" data-target="#add_new_modal"
                                    >Sửa</a></td>
                            </tr>
                                @endforeach

                            </tbody>
                        </table>

                        {!! $users->render();  !!}
                    </div>
                </div>
            </div>
        </div>
    </main>

    <div class="modal" id="add_new_modal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Thêm mới người dùng</h5>

                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="user_form" name="user_form">
                    <div class="modal-body">
                        <div class="card-body">
                            {{ csrf_field() }}
                            <input type="hidden" name="user_id" id="user_id" value="0" />
                            <div class="row">

                                <div class="col-sm-12">

                                    <div class="form-group">
                                        <label for="name">Họ và tên</label>
                                        <input type="text" class="form-control" id="full_name" name="full_name" required placeholder="">
                                    </div>

                                </div>

                            </div>

                            <div class="row">

                                <div class="col-sm-12">

                                    <div class="form-group">
                                        <label for="name">Tên đăng nhập</label>
                                        <input type="text" class="form-control" id="user_name" name="user_name" required placeholder="">
                                    </div>

                                </div>

                            </div>

                            <div class="row">

                                <div class="col-sm-12">

                                    <div class="form-group">
                                        <label for="name">Địa chỉ Email</label>
                                        <input type="email" class="form-control" id="email" name="email" required placeholder="">
                                    </div>

                                </div>

                            </div>
                            <!--/.row-->

                            <div class="row">

                                <div class="col-sm-12">

                                    <div class="form-group">
                                        <label for="name">Số điện thoại</label>
                                        <input type="text" class="form-control" id="phone" name="phone" required placeholder="">
                                    </div>

                                </div>

                            </div>

                            <div class="row">

                                <div class="col-sm-12">

                                    <div class="form-group">
                                        <label for="name">Mật khẩu</label>
                                        <input type="text" class="form-control" id="password" name="password" placeholder="">
                                    </div>

                                </div>

                            </div>
                            <!--/.row-->

                            <div class="row">

                                <div class="col-sm-12">

                                    <div class="form-group">
                                        <label for="name">Vai trò</label>
                                        <select class="form-control" id="is_role" name="is_role">
                                            {!! \App\Helpers\Constant::optionUserRole() !!}
                                        </select>
                                    </div>

                                </div>

                            </div>
                            <!--/.row-->

                            <div class="row">

                                <div class="col-sm-12">

                                    <div class="form-group">
                                        <label for="name">Trạng thái</label>
                                        <select class="form-control" id="is_active" name="is_active">
                                            {!! \App\Helpers\Constant::optionOnOff() !!}
                                        </select>
                                    </div>

                                </div>

                            </div>
                            <!--/.row-->

                        </div>
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="id" id="id" value="0" />
                        <button type="submit" class="btn btn-primary">Cập nhật</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        var User = {
            userEdit: '{{ route('admin.user_edit') }}',
            userUpdate: '{{ route('admin.user_update') }}',
            init: function () {
                $('#user_form').submit(function () {
                    var user_id = $('#user_id').val();
                    var password = $('#password').val();

                    if(user_id === 0 && !password) {
                        $('#password').focus();
                        return false;
                    }

                    return User.saveUser();
                });
            },
            search: function () {
                $('#search-form-user').submit();
            },
            edit: function(_this) {
                var full_name = $(_this).attr("full_name");
                var user_name = $(_this).attr("user_name");
                var phone = $(_this).attr("phone");
                var email = $(_this).attr("email");
                var is_role = $(_this).attr("is_role");
                var id = $(_this).attr("id");
                var is_active = $(_this).attr("is_active");

                $('#user_id').val(id);
                $('#full_name').val(full_name);
                $('#user_name').val(user_name);
                $('#phone').val(phone);
                $('#email').val(email);
                $('#is_role').val(is_role);
                $('#is_active').val(is_active);

            },
            saveUser: function () {
                SystemScript.postAjax(User.userUpdate, $('#user_form').serialize(), function (response) {
                    window.location.reload();
                });
                return false;
            },
            updateStatus: function (id) {
                console.log('updateStatus')
                SystemScript.postAjax('{{ route('admin.user_status_update') }}', {id}, function (response) {
                    $('#row_status_' + id).html(response.data.is_active == 1 ? 'Active' : 'InActive');
                })
            },
        }

        $(function () {
            User.init();
        })
    </script>
@endsection
