@extends('layouts.admin.admin')

@section('content')
    <style>
        .col-form-label {
            text-align: right;
        }
    </style>
    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Home</li>

            <li class="breadcrumb-item active">Danh sách Nhân viên</li>

        </ol>

        <div class="container-fluid">
            <div class="animated fadeIn">
                <div class="card">
                    <div class="card-header">
                        <strong>Nhân viên</strong>
                        T<PERSON><PERSON> kiếm
                    </div>
                    <div class="card-body">
                        <form id="search-order" action="" method="get" class="form-horizontal">
                            <div class="row">
                                <div class="form-group row col-md-4">
                                    <label class="col-md-4 col-form-label" for="hf-email"><PERSON><PERSON> điện thoại</label>
                                    <div class="col-md-8">
                                        <input type="text" id="phone" value="{{ $request->get('phone') }}" name="phone" class="form-control" placeholder="">
                                    </div>
                                </div>
                                <div class="form-group row col-md-4">
                                    <label class="col-md-4 col-form-label" for="hf-password">Họ tên</label>
                                    <div class="col-md-8">
                                        <input type="text" id="name" value="{{ $request->get('name') }}" name="name" class="form-control" placeholder="">
                                    </div>
                                </div>
                                <div class="form-group row col-md-4">
                                    <label class="col-md-4 col-form-label" for="hf-email">Email</label>
                                    <div class="col-md-8">
                                        <input type="text" id="email" name="email" value="{{ $request->get('email') }}" class="form-control" placeholder=" ">
                                    </div>
                                </div>

                            </div>
                        </form>
                    </div>
                    <div class="card-footer">
                        <button type="submit" onclick=" return OrderList.submit()" class="btn btn-sm btn-primary"><i class="fa fa-dot-circle-o"></i> Tìm kiếm</button>
                        <button type="reset" onclick="OrderList.reset()" class="btn btn-sm btn-danger"><i class="fa fa-ban"></i> Làm lại</button>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fa fa-edit"></i> Danh sách
                        <div class="card-actions">
                            <a href="https://datatables.net">
                                <small class="text-muted">docs</small>
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-responsive-sm table-hover table-outline mb-0">
                            <thead class="thead-light">
                            <tr>
                                <th class="text-center"><i class="icon-people"></i></th>
                                <th>Họ tên</th>
                                <th class="text-center">Số điện thoại</th>
                                <th class="text-center">Email</th>
                                <th>Trạng thái</th>
                                <th class="text-center">Đơn hàng</th>
                                <th class="text-center">Thao tác</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($users as $user)
                                <tr>
                                    <td class="text-center">
                                        <div class="avatar">
                                            <img src="{{ $user->avatar ? $user->avatar : '/images/avatar_default.png' }}" class="img-avatar" alt="<EMAIL>">
                                            {{--<span class="avatar-status badge-success"></span>--}}
                                        </div>
                                    </td>
                                    <td>
                                        <div>{{ $user->name }}</div>
                                        <div class="small text-muted">
                                            ĐKý: {{ $user->created_at }}
                                        </div>
                                    </td>

                                    <td>
                                        {{ $user->phone }}
                                    </td>
                                    <td class="text-center">
                                        {{ $user->email }}
                                    </td>
                                    <td>
                                        {{ $user->is_active == 1 ? 'Hoạt động' : 'Chưa Hoạt động' }}
                                    </td>
                                    <td class="text-center">
                                        <a href="{{ route('admin.order') }}?user_id={{ $user->id }}">Xem</a>
                                    </td>
                                    <td class="text-center">
                                        <a class="btn btn-info" href="{{ route('admin.user_update', ['id' => $user->id]) }}">
                                            <i class="fa fa-edit "></i>
                                        </a>
                                    </td>
                                </tr>
                            @endforeach

                            </tbody>
                        </table>

                        {!! $users->render();  !!}
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        $(function () {

        });
    </script>
@endsection
