@extends('layouts.admin.admin')

@section('content')

    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Admin</li>
            <li class="breadcrumb-item"><a href="#">User</a></li>
            <li class="breadcrumb-item active">Add or Update</li>
        </ol>

        <div class="container-fluid">

            <div class="animated fadeIn">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header">
                                <i class="fa fa-edits"></i>
                                <div class="card-actions">

                                </div>
                            </div>
                            <div class="card-body collapse show" id="collapseExample">
                                <form class="form-horizontal" method="post" id="updateForm">
                                    <input type="hidden" value="{{ $user->id }}" name="_id" id="_id" />
                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Họ tên</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="full_name" name="full_name" class="form-control" value="{{ $user->full_name }}" type="text">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Điện thoại</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="phone" name="phone" class="form-control" value="{{ $user->phone }}" type="text">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Username</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="user_name" name="user_name" class="form-control" value="{{ $user->user_name }}" type="text" {{ $user->user_name == 'admin' ? 'disabled' : '' }}>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Email</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="email" name="email" value="{{ $user->email }}" class="form-control" type="text">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Mật khẩu <i style="color: blue">(không bắt buộc nếu chỉnh sửa)</i></label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="password"  name="password" class="form-control" type="password">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Nhập lại mật khẩu <i style="color: blue">(không bắt buộc nếu chỉnh sửa)</i></label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="repassword" type="password" name="repassword" class="form-control">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Trạng thái hoạt động</label>
                                        <div class="controls">
                                            <select class="form-control" id="is_active" name="is_active">
                                                <option {{ $user->is_active == 1 ? 'selected' : '' }} value="1">Hoạt dộng</option>
                                                <option {{ $user->is_active == 0 ? 'selected' : '' }} {{ $user->user_name == 'admin' ? 'disabled' : '' }} value="0">Không hoạt động</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Role</label>
                                        <div class="controls">
                                            <select class="form-control" id="is_role" name="is_role">
                                                <option {{ $user->is_role != 2 ? 'selected' : '' }} {{ $user->user_name == 'admin' ? 'disabled' : '' }} value="1">Nhân viên</option>
                                                <option {{ $user->is_role == 2 ? 'selected' : '' }} value="2">Admin</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group row" id="permission-box" style="{{ $user->is_role == 2 ? 'display: none' : '' }}">
                                        <label class="col-md-4 col-form-label">Chọn Quyền</label>
                                        <div class="col-md-4 col-form-label">
                                            <div class="form-check checkbox">
                                                <input class="form-check-input" {{ in_array('CREATE_ONLINE_ORDER', $permissions) ? 'checked' : '' }} type="checkbox" value="CREATE_ONLINE_ORDER" id="create_online_order" name="permissions[]">
                                                <label class="form-check-label" for="create_online_order">
                                                    Tạo hoá đơn ONLINE
                                                </label>
                                            </div>
                                            <div class="form-check checkbox">
                                                <input class="form-check-input" {{ in_array('CREATE_OFFLINE_ORDER', $permissions) ? 'checked' : '' }} type="checkbox" value="CREATE_OFFLINE_ORDER" id="create_offline_order" name="permissions[]">
                                                <label class="form-check-label" for="create_offline_order">
                                                    Tạo hoá đơn OFFLINE
                                                </label>
                                            </div>
                                            <div class="form-check checkbox">
                                                <input class="form-check-input" {{ in_array('CREATE_ONLINE_DOCUMENT', $permissions) ? 'checked' : '' }} type="checkbox" value="CREATE_ONLINE_DOCUMENT" id="create_offline_document" name="permissions[]">
                                                <label class="form-check-label" for="create_offline_document">
                                                    Kiểm tra tài liệu ONLINE
                                                </label>
                                            </div>
                                        </div>

                                        <div class="col-md-4 col-form-label">
                                            <div class="form-check checkbox">
                                                <input class="form-check-input" {{ in_array('REPORT_ONLINE_ORDER', $permissions) ? 'checked' : '' }} type="checkbox" value="REPORT_ONLINE_ORDER" id="report_online_order" name="permissions[]">
                                                <label class="form-check-label" for="report_online_order">
                                                    Thống kê hoá đơn ONLINE
                                                </label>
                                            </div>
                                            <div class="form-check checkbox">
                                                <input class="form-check-input" {{ in_array('REPORT_OFFLINE_ORDER', $permissions) ? 'checked' : '' }} type="checkbox" value="REPORT_OFFLINE_ORDER" id="report_offline_order" name="permissions[]">
                                                <label class="form-check-label" for="report_offline_order">
                                                    Thống kê hoá đơn OFFLINE
                                                </label>
                                            </div>
                                            <div class="form-check checkbox">
                                                <input class="form-check-input" {{ in_array('REPORT_ONLINE_DOCUMENT', $permissions) ? 'checked' : '' }} type="checkbox" value="REPORT_ONLINE_DOCUMENT" id="report_online_document" name="permissions[]">
                                                <label class="form-check-label" for="report_online_document">
                                                    Thống kê tài liệu ONLINE
                                                </label>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">Update</button>
                                    </div>
                                    @csrf
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <!--/.row-->
            </div>

        </div>
        <!-- /.conainer-fluid -->
    </main>

    <script>
        $(function () {
            $.validator.setDefaults( {
                submitHandler: function () {
                    if($('#password').val() != $('#repassword').val()) {
                        $.alert({
                            title: 'Alert!',
                            content: 'Nhập hai mật khâủ phải giống nhau',
                        });
                        return  false;
                    }
                    return true;
                }
            });

            jQuery('#is_role').on('change', function () {
                if($(this).val() == 1) {
                    jQuery('#permission-box').show();
                } else {
                    jQuery('#permission-box').hide();
                }
            });

            $('#updateForm').validate({
                rules: {
                    full_name: {
                        required: true,
                        minlength: 5
                    },
                    phone: {
                        required: false,
                        minlength: 10,
                        number:true
                    },
                    email: {
                        required: true,
                        email:true
                    },
                    user_name: {
                        required: true,
                        minlength:5
                    }
                },
                messages: {
                    full_name: 'Field is required',
                    phone: 'Field is required',
                    email: 'Field is required',
                    password: 'Field is required',
                    repassword: 'Field is required',
                },
                errorElement: 'em',
                errorPlacement: function ( error, element ) {
                    error.addClass( 'invalid-feedback' );
                    if ( element.prop( 'type' ) === 'checkbox' ) {
                        error.insertAfter( element.parent( 'label' ) );
                    } else {
                        error.insertAfter( element );
                    }
                },
                highlight: function ( element, errorClass, validClass ) {
                    $( element ).addClass( 'is-invalid' ).removeClass( 'is-valid' );
                },
                unhighlight: function (element, errorClass, validClass) {
                    $( element ).addClass( 'is-valid' ).removeClass( 'is-invalid' );
                }
            });
        });
    </script>
@endsection
