@extends('layouts.admin.admin')
@section('content')
    <style>
        .badge {
            margin: 5px;
            position: relative;
            padding: 7px;
        }

        .badge a {
            color: #181818;
            vertical-align: middle;
        }

        .remove {
            color: red;
            font-weight: bold;
            border: 0;
            background-color: transparent;
            font-size: 1.2rem;
            vertical-align: middle;
        }
        .badge-pill {
            border: 2px solid #20a8d8;
        }
    </style>
    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Home</li>
            <li class="breadcrumb-item active"><PERSON>h sách <PERSON>ền</li>
        </ol>

        <div class="container-fluid">
            <div class="animated fadeIn">

                <div class="card">
                    <div class="card-header">
                        <i class="fa fa-edit"></i>Danh sách Quyền
                        <div class="card-actions">

                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-responsive-sm table-hover table-outline mb-0">
                            <thead class="thead-light">
                            <tr>
                                <th style="width: 320px">Tên <PERSON>ền</th>
                                <th>Th<PERSON>nh viên</th>
                                <th>Nhóm</th>
                                <th>Thao tác</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach(\App\Helpers\Constant::PERMISSION_DATA as $key => $info)
                                <tr>
                                    <td style="max-width: 320px">
                                        <div><strong>{{ $info['title'] }}</strong></div>
                                        <div>({{ $key }})</div>
                                    </td>
                                    <td>
                                        <div>
                                            @if(!empty($usersPermission[$key]))
                                                @foreach($usersPermission[$key] as $row)
                                                <div class="badge badge-pill">
                                                    <a href="#">{{ $row->full_name }}</a>
                                                    <button type="button" class="remove" aria-label="Remove" title="Xóa" onclick="Permission.remove({{ $row->id }})">
                                                        <span aria-hidden="true">×</span>
                                                    </button>
                                                </div>
                                                @endforeach
                                            @endif
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            @if(!empty($groupsPermission[$key]))
                                                @foreach($groupsPermission[$key] as $row)
                                                <div class="badge badge-pill">
                                                    <a href="#">{{ $row->group_name }}</a>
                                                    <button type="button" class="remove" aria-label="Remove" title="Xóa" onclick="Permission.remove({{ $row->id }})">
                                                        <span aria-hidden="true">×</span>
                                                    </button>
                                                </div>
                                                @endforeach
                                            @endif
                                        </div>
                                    </td>

                                    <td>
                                        <div><a href="#" data-toggle="modal" data-target="#add_user_modal" onclick="Permission.showModal('{{ $key }}')"><strong>[ Thêm ]</strong></a></div>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <div class="modal" id="add_user_modal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Thêm người dùng hoặc nhóm <span class="modal-group-name"></span></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="group_form_add_user" name="group_form">
                    <div class="modal-body">
                        <div class="card-body">
                            {{ csrf_field() }}
                            <input type="hidden" id="permission_key" name="permission_key" value=""/>

                            <div class="row">
                                <label class="form-row col-sm-12 users"><strong>Chọn Người dùng:</strong></label>
                            </div>

                            <div class="row">
                                <div class="form-row col-sm-12 users">
                                    @if(!empty($users))
                                        @foreach($users as $user)
                                            <div class="col-md-4">
                                                <div class="form-check form-check-inline">
                                                    <input type="checkbox" class="form-check-input" name="user_id[]" value="{{ $user->id }}" id="adduser-{{ $user->id }}">
                                                    <label class="form-check-label" for="adduser-{{ $user->id }}">{{ $user->full_name }}</label>
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif

                                </div>
                            </div>

                            <div class="row" style="margin-top: 25px">
                                <label class="form-row col-sm-12 users"><strong>Chọn Nhóm:</strong></label>
                            </div>

                            <div class="row">
                                <div class="form-row col-sm-12 groups">
                                    @if(!empty($groups))
                                        @foreach($groups as $group)
                                            <div class="col-md-4">
                                                <div class="form-check form-check-inline">
                                                    <input type="checkbox" class="form-check-input" name="group_id[]" value="{{ $group->id }}" id="addgroup-{{ $group->id }}">
                                                    <label class="form-check-label" for="addgroup-{{ $group->id }}">{{ $group->group_name }}</label>
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary" onclick="Permission.update()">Cập nhật</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
        var Permission = {
            usersPermission: {!! json_encode($usersPermission) !!},
            groupsPermission: {!! json_encode($groupsPermission) !!},
            permission_update: '{{ route('admin.user_permission_update') }}',
            permission_remove: '{{ route('admin.user_permission_remove') }}',

            init: function () {
                $('#group_form').submit(Permission.saveGroup);
                $('#group_form_edit').submit(function (e) {
                    e.preventDefault();
                    let id = $(this).find('[name="group_id"]').val();
                    return Permission.update(id);
                });
                $('#group_form_add_user').submit(function (e) {
                    e.preventDefault();
                    let checked = [];
                    $.each($(this).find('.form-check-input:checked'), function () {
                        checked.push($(this).val());
                    });
                    if (checked) {
                        let groupId = $('#group_form_add_user [name="group_id"]').val();
                        Permission.addUsers(groupId ,checked);
                    }
                });
            },
            update: function() {
                SystemScript.postAjax(Permission.permission_update, $('#group_form_add_user').serialize(), function (response) {
                    if(response.code == 0) {
                        window.location.reload();
                    } else {
                        alert(response.message)
                    }
                })
            },
            remove: function(id) {
                if(confirm('Chắc chắn xoá nội dung này chứ?')) {
                    SystemScript.postAjax(Permission.permission_remove, {id}, function (response) {
                        if (response.code == 0) {
                            window.location.reload();
                        } else {
                            alert(response.message)
                        }
                    })
                }
            },
            showModal: function(key) {
                $('#group_form_add_user input').prop('checked', false);
                $('#group_form_add_user').find('#permission_key').val(key);
                if(Permission.usersPermission[key] !== undefined) {
                    Permission.usersPermission[key].forEach(function (user) {
                        $('#adduser-' + user.user_id).prop('checked', true);
                    });
                }

                if(Permission.groupsPermission[key] !== undefined) {
                    Permission.groupsPermission[key].forEach(function (group) {
                        $('#addgroup-' + group.group_id).prop('checked', true);
                    });
                }
            },
        };
        $(function () {
            Permission.init();
        })
    </script>
@endsection