
@extends('layouts.admin.admin')

@section('content')

    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Home</li>

            <li class="breadcrumb-item active">Remote Login</li>
            <!-- Breadcrumb Menu-->
            <li class="breadcrumb-menu d-md-down-none">

            </li>
        </ol>

        <div class="container-fluid">
            <div class="animated fadeIn">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header">
                                <i class="icon-cursor"></i> Remote Login
                                <div class="card-actions">
                                    <a href="https://github.com/jzaefferer/jquery-validation">
                                        <small class="text-muted">docs</small>
                                    </a>
                                </div>
                            </div>
                            <div class="card-body collapse show" id="collapseExample">
                                @if(!$remoteSession || $remoteSession['user_id'] != $user->id)
                                <form class="form-horizontal" method="post">
                                    @csrf
                                    <div class="form-group" style="margin-bottom: 1px">
                                        <label class="col-form-label" for="username">Username:</label>
                                        <div class="input-group">

                                            <input type="text" id="username" name="username" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-form-label" for="password">Password:</label>
                                        <div class="input-group">

                                            <input type="password" id="password" name="password" class="form-control" placeholder="">
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" onclick="onLoading()" class="btn btn-primary">Login & Save</button>
                                    </div>
                                </form>
                                @else
                                <div class="alert alert-success" role="alert">
                                    Bạn đã đăng nhập V4U thành công: <strong style="color: orangered">[{{ $remoteSession['username'] }}]</strong> <a href="{{ route('admin.remote_logout') }}" class="alert-link"><strong style="color: blue">Nhấn vào đây để Thoát</strong></a>.
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <!-- /.conainer-fluid -->
    </main>
@endsection
