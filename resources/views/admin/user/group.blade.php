@extends('layouts.admin.admin')
@section('content')
    <style>
        .badge {
            margin: 5px;
            position: relative;
            padding: 7px;
        }

        .badge a {
            color: #181818;
            vertical-align: middle;
        }

        .remove {
            color: red;
            font-weight: bold;
            border: 0;
            background-color: transparent;
            font-size: 1.2rem;
            vertical-align: middle;
        }
        .badge-pill {
            border: 2px solid #20a8d8;
        }
    </style>
    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Home</li>
            <li class="breadcrumb-item active">Danh sách nhóm</li>
        </ol>

        <div class="container-fluid">
            <div class="animated fadeIn">
                <div class="card">
                    <div class="card-header">
                        <strong>Quản lý nhóm</strong>
                    </div>
                    <div class="card-body">
                        <form id="search-group" action="" method="get" class="form-horizontal">
                            <div class="row">
                                <div class="form-group row col-md-12">
                                    <label class="col-md-2 col-form-label" for="hf-password">Tên nhóm</label>
                                    <div class="col-md-10">
                                        <input type="text" id="group_name" value="{{ $request->get('name') }}" name="name" class="form-control" placeholder="">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer">
                        <button type="submit" onclick="$('#search-group').submit()" class="btn btn-sm btn-primary"><i class="fa fa-dot-circle-o"></i> Tìm</button>
                        <button type="reset" onclick="window.location = '/admin/user/group/list';" class="btn btn-sm btn-danger"><i class="fa fa-ban"></i> Làm lại</button>
                        @if(\Illuminate\Support\Facades\Auth::user()->is_role == 2)
                            <button type="reset" class="btn btn-sm btn-success" data-toggle="modal" data-target="#add_new_modal"><i class="fa fa-plus"></i> Thêm nhóm</button>
                        @endif
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fa fa-edit"></i>Danh sách nhóm ({{ $groups ? number_format(count($groups)) : 0 }})
                        <div class="card-actions">

                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-responsive-sm table-hover table-outline mb-0">
                            <thead class="thead-light">
                            <tr>
                                <th>Tên nhóm</th>
                                <th>Thành viên</th>
                                <th>Trạng thái</th>
                                <th>Ngày tạo</th>
                                @if(\Illuminate\Support\Facades\Auth::user()->is_role == 2)
                                <th>Thao tác</th>
                                @endif
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($groups as $group)
                            <tr>
                                <td>
                                    <div class="name">{{ $group->group_name }}</div>
                                </td>
                                <td>
                                    @foreach($group->users as $user)
                                    <div class="badge badge-pill">
                                        <a href="{{route('admin.user_update')}}?id={{$user->id}}">{{$user->full_name}}</a>
                                        <button type="button" class="remove" aria-label="Remove" title="Xóa" onclick="Group.removeUser('{{$group->id}}', '{{$user->id}}', '{{$group->group_name}}', '{{$user->full_name}}')">
                                            <span aria-hidden="true">&times;</span>
                                        </button>
                                    </div>
                                    @endforeach
                                </td>
                                <td>
                                    <div id="row_status_{{ $group->id }}">{{ $group->status == 1 ? 'Hoạt động' : 'Không hoạt động' }}</div>
                                </td>
                                <td>
                                    {{ $group->created_at }}
                                </td>
                                @if(\Illuminate\Support\Facades\Auth::user()->is_role == 2)
                                <td>
                                    <a href="javascript:void(0);" onclick="Group.showModal('add_user', '{{$group->id}}', '{{$group->group_name}}', '{{$group->status}}', {{$userIds = $group->users()->pluck('user_id')}})" data-toggle="modal" data-target="#add_user_modal">Thêm người dùng</a> |
                                    <a href="javascript:void(0);" onclick="Group.showModal('edit', '{{$group->id}}', '{{$group->group_name}}', '{{$group->status}}' )" data-toggle="modal" data-target="#edit_modal">Sửa</a> |
                                    <a href="javascript:void(0);" onclick="Group.delete('{{$group->id}}', '{{$group->group_name}}');">Xóa</a>
                                </td>
                                @endif
                            </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <div class="modal" id="add_new_modal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Thêm mới nhóm</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="group_form" name="group_form">
                    <div class="modal-body">
                        <div class="card-body">
                            {{ csrf_field() }}
                            <input type="hidden" name="group_id" id="group_id" value="0" />
                            <input type="hidden" name="status" id="status" value="1" />
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label for="group_name">Tên nhóm</label>
                                        <input type="text" class="form-control" name="group_name" required placeholder="">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">Cập nhật</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="modal" id="edit_modal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Sửa nhóm</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="group_form_edit" name="group_form">
                    <div class="modal-body">
                        <div class="card-body">
                            {{ csrf_field() }}
                            <input type="hidden" name="group_id"/>
                            <input type="hidden" name="status"/>
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label for="group_name">Tên nhóm</label>
                                        <input type="text" class="form-control" name="group_name" required placeholder="">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">Cập nhật</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="modal" id="add_user_modal">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Thêm người dùng vào nhóm <span class="modal-group-name"></span></h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <form id="group_form_add_user" name="group_form">
                    <div class="modal-body">
                        <div class="card-body">
                            {{ csrf_field() }}
                            <input type="hidden" name="group_id"/>
                            <input type="hidden" name="group_name"/>
                            <input type="hidden" name="status"/>
                            <div class="row">
                                <div class="form-row col-sm-12 users">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary">Cập nhật</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <script>
        var Group = {
            groupEdit: '{{ route('admin.user_group_update') }}',
            groupUpdate: '{{ route('admin.user_group_update') }}',
            init: function () {
                $('#group_form').submit(Group.saveGroup);
                $('#group_form_edit').submit(function (e) {
                    e.preventDefault();
                    let id = $(this).find('[name="group_id"]').val();
                    return Group.update(id);
                });
                $('#group_form_add_user').submit(function (e) {
                    e.preventDefault();
                    let checked = [];
                    $.each($(this).find('.form-check-input:checked'), function () {
                        checked.push($(this).val());
                    });
                    if (checked) {
                        let groupId = $('#group_form_add_user [name="group_id"]').val();
                        Group.addUsers(groupId ,checked);
                    }
                });
            },
            search: function () {
                $('#search-group').submit();
            },
            saveGroup: function () {
                SystemScript.postAjax(Group.groupUpdate, $('#group_form').serialize(), function (response) {
                    if(response.data) {
                        window.location = response.data;
                    }
                });
                return false;
            },
            update: function(id) {
              SystemScript.postAjax(Group.groupUpdate + '/' + id, $('#group_form_edit').serialize(), function (response) {
                  console.log('update', id);
                  if(response.data) {
                      window.location.reload();
                  }
              })
            },
            updateStatus: function (id) {
                SystemScript.postAjax('{{ route('admin.user_group_update_status') }}', {id}, function (response) {
                    $('#row_status_' + id).html(response.data.status == 1 ? 'Hoạt động' : 'Không hoạt động');
                });
            },
            delete: function(id, name) {
                if (!confirm('Bạn có chắc muốn xóa nhóm [' + name + '] không?')) {
                    return;
                }
                SystemScript.getAjax('{{route('admin.user_group_delete')}}' + '/' + id, null, function (response) {
                    if (response.data) {
                        window.location.reload();
                    }
                });
            },
            showModal: function(action, id, name, status, users) {
                let key = '#add_new_modal';
                if (action === 'edit') {
                    key = '#edit_modal';
                } else if (action === 'add_user') {
                    key = '#add_user_modal';
                    $(key + ' .modal-group-name').text(name);
                    Group.loadAllUsers(users);
                }
                $(key + ' [name="group_id"]').val(id);
                $(key + ' [name="group_name"]').val(name);
                $(key + ' [name="status"]').val(status);
            },
            loadAllUsers: function(addedUsers) {
                SystemScript.getAjax('{{route('admin.user_list')}}', null, function(response) {
                    if (response.data) {
                        let container = $('#group_form_add_user .users');
                        let users = response.data;
                        container.html('Không tồn tại người dùng phù hợp để thêm vào nhóm!');
                        if (users) {
                            users = users.filter(item => addedUsers.indexOf(item.id) < 0);
                            if (users.length > 0) {
                                container.html('');
                            }
                            users.forEach(item => {
                                let inputContainer = $('<div class="form-check form-check-inline"></div>');
                                let userInput = $('<input type="checkbox">').addClass('form-check-input').val(item.id).attr('id', 'adduser-' + item.id);
                                let userLabel = $('<label></label>').addClass('form-check-label').attr('for', 'adduser-' + item.id).text(item.full_name);
                                inputContainer.append(userInput).append(userLabel);
                                container.append($('<div class="col-md-4"></div>').append(inputContainer));
                            });
                        }
                    }
                });
            },
            removeUser: function (groupId, userId, groupName, userName) {
                if (!confirm('Bạn có chắc muốn xóa nguời dùng [' + userName + '] khỏi nhóm [' + groupName + '] ?')) {
                    return;
                }
                SystemScript.postAjax('{{route('admin.user_group_remove_user')}}' + '/' + groupId, { userId }, function (response) {
                    window.location.reload();
                })
            },
            addUsers: function (groupId, userIds) {
                console.log(userIds);
                SystemScript.postAjax('{{route('admin.user_group_add_user')}}' + '/' + groupId, {userIds}, function (response) {
                    window.location.reload();
                })
            }
        };
        $(function () {
            Group.init();
        })
        </script>
@endsection