
@extends('layouts.admin.admin')

@section('content')

    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Trang chủ</li>

            <li class="breadcrumb-item active">Trang tĩnh</li>

        </ol>

        <div class="container-fluid">
            <div class="animated fadeIn">
                <div class="card">
                    <div class="card-header bg-primary">
                        <strong>Tìm kiếm</strong>
                    </div>
                    <div class="card-body">
                        <form id="search-news" name="search-news" action="" method="get" class="form-horizontal">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group row">
                                        <label class="col-md-3 col-form-label" for="hf-email">Từ cần tìm</label>
                                        <div class="col-md-9">
                                            <input type="text" id="title" value="{{ $request->get('title') }}" name="title" class="form-control" placeholder="">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer">
                        <button type="submit" onclick=" return NewsList.submit()" class="btn btn-sm btn-primary"><i class="fa fa-dot-circle-o"></i> Tìm kiếm</button>
                        <button type="reset" onclick="NewsList.reset()" class="btn btn-sm btn-danger"><i class="fa fa-ban"></i> Làm lại</button>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fa fa-edit"></i> Danh sách Trang (Tổng số: {{ !empty($staticPages) ? number_format(\App\Models\Page::count()) : 0 }})
                        <div class="card-actions">
                            <a href="https://datatables.net">
                                <small class="text-muted">docs</small>
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-responsive-sm table-bordered table-striped table-sm">
                            <thead class="thead-light">
                            <tr>
                                <th>#</th>
                                <th>Tiêu đề</th>
                                <th>Tiêu đề SEO</th>
                                <th>Mô tả</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                            </thead>
                            <tbody>
                            @if($staticPages)
                                @foreach($staticPages as $idx => $row)
                                    <tr>
                                        <td>{{ ($request->input('page', 1) - 1) * \App\Helpers\Constant::ITEM_PER_PAGE + ($idx + 1) }}</td>

                                        <td align="left" style="word-wrap: break-word;max-width: 500px;">
                                            <a target="_blank" href="{{ route('static_page', ['page_title' => \App\Helpers\Utils::slugify($row->title)]) }}" title="Mở trang">{!! ($row->title) !!}</a>
                                        </td>
                                        <td>{{ $row->title_seo }}</td>
                                        <td>{{ $row->description }}</td>

                                        <td align="center" style="padding-bottom: 15px;text-align: center;font-weight: bold;">
                                            <span class="news-status" id="news-status-label-{{ $row->id }}">{{ \App\Helpers\Constant::STATUS_ON_OFF_LABEL[$row->status] }}</span>
                                            <div class="form-check checkbox" style="margin-bottom: 15px;">
                                                <input class="form-check-input" {{ $row->status == 1 ? 'checked' : '' }} onchange="NewsList.statusChange({{ $row->id }})" type="checkbox" value="" id="news-status-{{ $row->id }}">
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.static_page_update', ['id' => $row->id]) }}">Sửa</a>
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
        <!-- /.conainer-fluid -->
    </main>

    <script>
        $(function () {
            NewsList.init();
        });

        const NewsList = {
            urlUpdate: '{{ route('admin.static_page_update_by_field') }}',
            init: function () {

            },
            submit: function () {
                $('#search-news').submit();
            },
            statusChange: function (id) {
                SystemScript.postAjax(NewsList.urlUpdate, {id: id, field: 'status', 'value': null}, function (res) {
                    console.log(res);
                    if(res.code == 0) {
                        offLoading();
                    } else {
                        alert(res.message);
                    }
                });
            },
            reset: function () {
                window.location.href = '{{ route('admin.static_page') }}';
            }
        }
    </script>
@endsection
