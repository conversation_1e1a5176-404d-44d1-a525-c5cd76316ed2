@extends('layouts.admin.admin')

@section('content')

    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Trang chủ</li>
            <li class="breadcrumb-item">Trang tĩnh</li>
        </ol>

        <div class="container-fluid">

            <div class="animated fadeIn">
                <div class="row">
                    <div class="col-lg-12">
                        <div class="card">
                            <div class="card-header">
                                <i class="fa fa-align-justify"></i>
                                Cập nhật thông tin
                            </div>
                            <div class="card-body collapse show" id="collapseExample">
                                <form class="form-horizontal" method="post" id="smtp-setting" novalidate="novalidate">
                                    @csrf
                                    <input type="hidden" value="{{ @$staticPage->id }}" name="id" id="id">
                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Tiêu đề</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="title" name="title" class="form-control" value="{{ @$staticPage->title }}" type="text" placeholder="">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Tiêu đề SEO</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <input id="title_seo" name="title_seo" class="form-control" value="{{ @$staticPage->title_seo }}" type="text" placeholder="">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Mô tả</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <textarea rows="4" class="form-control" id="description" name="description">{{ @$staticPage->description }}</textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label class="col-form-label" for="appendedInput">Nội dung Trang</label>
                                        <div class="controls">
                                            <div class="input-group">
                                                <textarea class="form-control" id="content" name="content">{{ @$staticPage->content }}</textarea>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">Cập nhật</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <!--/.row-->
            </div>

        </div>
        <!-- /.conainer-fluid -->
    </main>

    <script src="{{ asset('/js/ckeditor/ckeditor.js') }}"></script>
    <script type="text/javascript" src="/js/ckfinder/ckfinder.js"></script>
    <script>
        CKFinder.config( { connectorPath: '/ckfinder/connector' } );
        $(function () {

            News.init();
        })

        const News = {
            baseUrl: '{{ url('/') }}',
            urlSave: '{{ route('admin.news_update') }}',
            init: function () {
                let editor = CKEDITOR.replace('content', {
                    width: '100%',
                    height: 700,
                    language: 'vi'
                });
                CKFinder.setupCKEditor( editor );
            },
            selectFileWithCKFinder: function ( elementId ) {
                CKFinder.popup({
                    chooseFiles: true,
                    width: 800,
                    height: 600,
                    language: 'vi',
                    onInit: function( finder ) {
                        finder.on( 'files:choose', function( evt ) {
                            var file = evt.data.files.first();
                            var output = document.getElementById( elementId );
                            output.value = file.getUrl().replace(News.baseUrl, '');
                            $('#photo_show').attr('src', file.getUrl());
                        } );

                        finder.on( 'file:choose:resizedImage', function( evt ) {
                            var output = document.getElementById( elementId );
                            $('#photo_show').attr('src', evt.data.resizedUrl);
                            output.value = evt.data.resizedUrl.replace(News.baseUrl, '');
                        } );
                    }
                });
            }
        };
    </script>
@endsection
