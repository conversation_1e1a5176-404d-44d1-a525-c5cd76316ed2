
@extends('layouts.admin.admin')

@section('content')

    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Trang chủ</li>

            <li class="breadcrumb-item active">Quảng cáo</li>

        </ol>

        <div class="container-fluid">
            <div class="animated fadeIn">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header bg-primary">
                                <strong id="header-title">Thêm mới</strong>

                                <span style="float: right;"><button onclick="ScriptCustom.new()" style="border: 1px solid #fff" class="btn btn-sm btn-green"><strong>Thêm mới</strong></button></span>
                            </div>
                            <div class="card-body">
                                <form id="adv-form" name="adv-form" action="{{ route('admin.advertisement_update') }}" method="post" class="form-horizontal">
                                    @csrf
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <div class="form-group row">
                                                <label class="col-md-3 col-form-label" for="hf-email">Tên quảng cáo</label>
                                                <div class="col-md-9">
                                                    <input type="text" id="title" value="" name="title" class="form-control" placeholder="">
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label class="col-md-3 col-form-label" for="hf-email">Vị trí</label>
                                                <div class="col-md-9">
                                                    <select class="form-control" id="position" name="position">
                                                        {!! \App\Helpers\Constant::optionAdvertisement() !!}
                                                    </select>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label class="col-md-3 col-form-label" for="hf-email">Ảnh</label>
                                                <div class="col-md-9">
                                                    <button type="button" class="form-control btn btn-warning" name="select-photo" id="select-photo" onclick="ScriptCustom.selectFileWithCKFinder('photo')">Ảnh từ Thư viện</button>
                                                    <input type="hidden" name="photo" id="photo" value="" />
                                                    <div class="form-group col-sm-12" style="padding: 15px;">
                                                        <img class="float-right" id="photo_show" src="/images/news_default.png" style="max-width: 100%;max-height: 900px;">
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label class="col-md-3 col-form-label" for="hf-email">Link quảng cáo</label>
                                                <div class="col-md-9">
                                                    <input type="text" id="redirect_url" value="" name="redirect_url" class="form-control" placeholder="">
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label class="col-md-3 col-form-label" for="hf-email">Thứ tự hiển thị</label>
                                                <div class="col-md-9">
                                                    <input type="text" id="sort" value="1" name="sort" class="form-control" placeholder="">
                                                </div>
                                            </div>

                                            <div class="form-group row">
                                                <label class="col-md-3 col-form-label" for="hf-email">Trạng thái</label>
                                                <div class="col-md-9">
                                                    <select class="form-control" id="status" name="status">
                                                        {!! \App\Helpers\Constant::optionOnOff() !!}
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <input type="hidden" id="id" name="id" value="" />
                                </form>
                            </div>
                            <div class="card-footer">
                                <button type="submit" onclick=" return ScriptCustom.submit()" class="btn btn-sm btn-primary"><i class="fa fa-dot-circle-o"></i> Cập nhật</button>
                                <button type="reset" onclick="ScriptCustom.reset()" class="btn btn-sm btn-danger"><i class="fa fa-ban"></i> Làm lại</button>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card">
                            <img style="width: 100%" src="/images/advertisement.png" />
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fa fa-edit"></i> Danh sách Quảng cáo
                        <div class="card-actions">
                            <a href="https://datatables.net">
                                <small class="text-muted">docs</small>
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <table class="table table-responsive-sm table-bordered table-striped table-sm">
                            <thead class="thead-light">
                            <tr>
                                <th>#</th>
                                <th>Tiêu đề</th>
                                <th>Ảnh</th>
                                <th>Vị trí</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                            </thead>
                            <tbody>
                            @if(!empty($data))
                                @foreach($data as $idx => $row)
                                    <tr>
                                        <td>{{ ($request->input('page', 1) - 1) * \App\Helpers\Constant::ITEM_PER_PAGE + ($idx + 1) }}</td>

                                        <td align="left" style="word-wrap: break-word;max-width: 500px;">
                                            <a target="_blank" href="{{ $row->redirect_url }}" title="Mở trang">{!! ($row->title) !!}</a>
                                        </td>
                                        <td>
                                            <img src="{{ $row->photo }}" />
                                        </td>
                                        <td>{{ \App\Helpers\Constant::ADVERTISEMENTS[$row->position] }}</td>

                                        <td align="center" style="padding-bottom: 15px;text-align: center;font-weight: bold;">
                                            <span class="news-status" id="news-status-label-{{ $row->id }}">{{ \App\Helpers\Constant::STATUS_ON_OFF_LABEL[$row->status] }}</span>
                                            <div class="form-check checkbox" style="margin-bottom: 15px;">
                                                <input class="form-check-input" {{ $row->status == 1 ? 'checked' : '' }} onchange="ScriptCustom.statusChange({{ $row->id }})" type="checkbox" value="" id="news-status-{{ $row->id }}">
                                            </div>
                                        </td>
                                        <td>
                                            <a onclick="return ScriptCustom.edit({{ $row->id }}, this)"
                                               title="{{ $row->title }}"
                                               photo="{{ $row->photo }}"
                                               redirect_url="{{ $row->redirect_url }}"
                                               status="{{ $row->status }}"
                                               sort="{{ $row->sort }}"
                                               position="{{ $row->position }}"
                                               href="">Sửa</a>
                                            |
                                            <a onclick="return confirm('Chắc chắn xoá quảng cáo này chứ?')" href="{{ route('admin.advertisement_delete', ['id' => $row->id]) }}">Xoá</a>
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
        <!-- /.conainer-fluid -->
    </main>

    <script type="text/javascript" src="/js/ckfinder/ckfinder.js"></script>

    <script>
        $(function () {
            ScriptCustom.init();
        });

        const ScriptCustom = {
            baseUrl: '{{ url('/') }}',
            urlUpdate: '{{ route('admin.advertisement_update_status') }}',
            init: function () {
                CKFinder.config( { connectorPath: '/ckfinder/connector', language: 'vi' } );
            },
            submit: function () {
                $('#adv-form').submit();
            },
            new: function() {
                $('#adv-form input[type="text"]').val("");
                $('#id').val("");
                $('#sort').val(1);
                $('#photo_show').attr('src', '/images/news_default.png');
                $('#header-title').html("Thêm mới");
            },
            edit: function (id, _this) {
                $('#id').val(id);
                $('#title').val($(_this).attr('title'));
                $('#photo').val($(_this).attr('photo'));
                $('#photo_show').attr('src', $(_this).attr('photo'));
                $('#redirect_url').val($(_this).attr('redirect_url'));
                $('#status').val($(_this).attr('status'));
                $('#position').val($(_this).attr('position'));
                $('#sort').val($(_this).attr('sort'));

                $('#header-title').html("Sửa Quảng Cáo");
                $('html, body').animate({
                    scrollTop: $("#header-title").offset().top - 80
                }, 1000);

                return false;
            },
            statusChange: function (id) {
                SystemScript.postAjax(ScriptCustom.urlUpdate, {id: id, field: 'status', 'value': null}, function (res) {
                    console.log(res);
                    if(res.code == 0) {
                        offLoading();
                    } else {
                        alert(res.message);
                    }
                });
            },
            selectFileWithCKFinder: function ( elementId ) {
                CKFinder.popup({
                    chooseFiles: true,
                    width: 800,
                    height: 600,
                    language: 'vi',
                    onInit: function( finder ) {
                        finder.on( 'files:choose', function( evt ) {
                            var file = evt.data.files.first();
                            var output = document.getElementById( elementId );
                            output.value = file.getUrl().replace(ScriptCustom.baseUrl, '');
                            $('#photo_show').attr('src', file.getUrl());
                        } );

                        finder.on( 'file:choose:resizedImage', function( evt ) {
                            var output = document.getElementById( elementId );
                            $('#photo_show').attr('src', evt.data.resizedUrl);
                            output.value = evt.data.resizedUrl.replace(ScriptCustom.baseUrl, '');
                        } );
                    }
                });
            },
            reset: function () {
                window.location.href = '{{ route('admin.advertisement') }}';
            }
        }
    </script>
@endsection
