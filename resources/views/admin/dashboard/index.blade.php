@extends('layouts.admin.admin')

@section('content')

    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li> Dashboard </li>
        </ol>

        <div class="container-fluid">

            <div class="animated fadeIn">
                <div class="row">

                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white bg-danger">
                            <div class="card-body pb-0 summary-box">
                                <h4 class="mb-0 summary-number">{{ number_format(App\Models\News::count()) }}</h4>
                                <p>Tổng số Bài viết</p>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->

                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white bg-warning">
                            <div class="card-body pb-0 summary-box">
                                <h4 class="mb-0 summary-number">{{ number_format(App\Models\Category::count()) }}</h4>
                                <p>Tổng số Danh mục</p>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->

                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white bg-success">
                            <div class="card-body pb-0 summary-box">
                                <h4 class="mb-0 summary-number">{{ number_format(App\Models\User::count()) }}</h4>
                                <p>Tổng số thành viên</p>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->

                    <div class="col-sm-6 col-lg-3">
                        <div class="card text-white bg-primary">
                            <div class="card-body pb-0 summary-box">
                                <h4 class="mb-0 summary-number">{{ number_format(App\Models\Page::count()) }}</h4>
                                <p>Tổng số Trang tĩnh</p>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->
                </div>
                <!--/.row-->

                <div class="card-group mb-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="h1 text-muted text-right mb-4">
                                <i class="icon-people"></i>
                            </div>
                            <div class="h1 mb-0 summary-number text-success">{{ number_format(intval($totalCounters->all)) }}</div>
                            <small class="text-muted text-uppercase font-weight-bold">Tổng số lượt truy cập</small>
                            <div class="progress progress-xs mt-3 mb-0">
                                <div class="progress-bar bg-info" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div class="h1 text-muted text-right mb-4">
                                <i class="icon-user-follow"></i>
                            </div>
                            <div class="h1 mb-0 summary-number text-primary">{{ number_format(intval($totalCounters->week)) }}</div>
                            <small class="text-muted text-uppercase font-weight-bold">
                                Số lượt truy cập trong tuần ({{ date("d/m/Y", strtotime($currentWeek['start_time'])) }} - {{ date("d/m/Y", strtotime($currentWeek['end_time'])) }})
                            </small>
                            <div class="progress progress-xs mt-3 mb-0">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div class="h1 text-muted text-right mb-4">
                                <i class="icon-speedometer"></i>
                            </div>
                            <div class="h1 mb-0 summary-number text-danger">{{ number_format(intval($totalCounters->day)) }}</div>
                            <small class="text-muted text-uppercase font-weight-bold">Số lượt truy cập trong ngày</small>
                            <div class="progress progress-xs mt-3 mb-0">
                                <div class="progress-bar bg-danger" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!--/.row-->

                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                Top các bài viết có lượt truy cập lớn nhất trong ngày:
                            </div>
                            <div class="card-body">

                                <br>
                                <table class="table table-responsive-sm table-bordered table-striped table-sm">
                                    <thead class="thead-light">
                                    <tr>
                                        <th>ID</th>
                                        <th>Tiêu đề</th>
                                        <th>Lượt truy cập</th>
                                        <th>Ngày</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @if(!empty($topViewed))
                                        @foreach($topViewed as $idx => $row)
                                            <tr>
                                                <td title="{{ $row->title }}">{{ $row->news_id }}</td>
                                                <td><a target="_blank" href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($row->title)]) }}">{{ $row->title }}</a></td>
                                                <td>{{ $row->counter }}</td>
                                                <td>{{ date("d/m/Y") }}</td>
                                            </tr>
                                        @endforeach
                                    @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <!--/.col-->
                </div>
                <!--/.row-->
            </div>

        </div>
        <!-- /.conainer-fluid -->
    </main>


@endsection
