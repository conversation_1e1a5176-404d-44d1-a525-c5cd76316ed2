@extends('layouts.admin.admin')
@section('content')
    <!-- Main content -->
    <main class="main">

        <!-- Breadcrumb -->
        <ol class="breadcrumb">
            <li class="breadcrumb-item">Admin</li>
            <li class="breadcrumb-item"><a href="#">Đ<PERSON><PERSON> mật khẩu</a></li>
            <li class="breadcrumb-item active">Cập nhật</li>
        </ol>

        <div class="container-fluid">

            <div class="animated fadeIn">
                <div class="row">
                    <div class="col-lg-12">
                        <!-- Styles -->
                        {{--    <link href="{{ asset('css/app.css') }}" rel="stylesheet">--}}
                        <section class="feature_area leadspace_page" style="padding-top: 20px;padding-bottom: 20px;">
                            <div class="container">
                                <div class="row justify-content-center">
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-header"
                                                 style="background-color: #3490dc;color: #ffffff;font-weight: bold;">
                                                Đ<PERSON><PERSON> mật khẩu
                                            </div>

                                            <div class="card-body">
                                                @if (session('error'))
                                                    <div class="alert alert-danger">
                                                        {{ session('error') }}
                                                    </div>
                                                @endif
                                                @if (session('success'))
                                                    <div class="alert alert-success">
                                                        {{ session('success') }}
                                                    </div>
                                                @endif
                                                <form class="form-horizontal" method="POST"
                                                      action="{{ route('changePassword') }}">
                                                    {{ csrf_field() }}

                                                    <div class="row form-group{{ $errors->has('current-password') ? ' has-error' : '' }}">
                                                        <label for="new-password" class="col-md-4 control-label">
                                                            Mật khẩu hiện tại
                                                        </label>

                                                        <div class="col-md-8">
                                                            <input id="current-password" type="password"
                                                                   class="form-control" name="current-password"
                                                                   required>

                                                            @if ($errors->has('current-password'))
                                                                <span class="help-block">
                                                                    <strong>{{ $errors->first('current-password') }}</strong>
                                                                </span>
                                                            @endif
                                                        </div>
                                                    </div>

                                                    <div class="row form-group{{ $errors->has('new-password') ? ' has-error' : '' }}">
                                                        <label for="new-password" class="col-md-4 control-label">Mật khẩu mới</label>

                                                        <div class="col-md-8">
                                                            <input id="new-password" type="password"
                                                                   class="form-control" name="new-password" required>

                                                            @if ($errors->has('new-password'))
                                                                <span class="help-block">
                                                                    <strong>{{ $errors->first('new-password') }}</strong>
                                                                </span>
                                                            @endif
                                                        </div>
                                                    </div>

                                                    <div class="row form-group">
                                                        <label for="new-password-confirm"
                                                               class="col-md-4 control-label">Nhập lại mật khẩu mới</label>

                                                        <div class="col-md-8">
                                                            <input id="new-password-confirm" type="password"
                                                                   class="form-control" name="new-password-confirm"
                                                                   required>
                                                        </div>
                                                    </div>

                                                    <div class="row form-group">
                                                        <div class="col-md-6 col-md-offset-4">
                                                            <button type="submit" class="btn btn-primary">
                                                                Cập nhật
                                                            </button>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </div>
    </main>
@endsection