@extends('layouts.frontend.index')

@section('content')

<section class="single-phatgiao">
    <div class="container pt-3 pb-3">
        <div class="row">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb mb-2">
                    <ol class="breadcrumb justify-content-between">
                        <li class="breadcrumb-item"><a href="https://phatgiaothanhhoa.com/lien-he.shtml#">Trang chủ</a>
                        </li>
                        <li class="breadcrumb-item active mr-auto" aria-current="page"><span><PERSON>ê<PERSON></span></li>
                        <li><small id="clockPC">Th<PERSON> bảy, 31/10/2020 | 23:21 GMT+7</small></li>
                    </ol>
                </nav>
                <div class="sing-content">
                    <article>
                        <h1>{{ $page->title }}</h1>
                        <h4 class="text-justify excrept">
                            <em>{{ $page->description }} </em>
                        </h4>

                        {!! $page->content  !!}

                    </article>

                    <div class="fb-share-button" data-href="http://www.facebook.com/BanTTTTPhatgiaoThanhHoa" data-layout="button" data-size="large">
                        <a target="_blank" href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Fwww.facebook.com%2FBanTTTTPhatgiaoThanhHoa&amp;src=sdkpreparse" class="fb-xfbml-parse-ignore">
                            Chia sẻ
                        </a>
                    </div>

                    <div class="fb-comments" data-href="http://www.facebook.com/BanTTTTPhatgiaoThanhHoa" data-numposts="5" data-width=""></div>


                </div>
            </div>

            <div class="col-lg-4">
                @include('layouts.frontend.partial.right', [
                    'news' => \App\Models\News::where('status', 1)->where('is_focus', 1)->orderBy('id', 'DESC')->get(),
                    'latestNews' => \App\Models\News::where('status', 1)->orderBy('id', 'DESC')->limit(10)->get()
                ])
            </div>
        </div>
    </div>
</section>

@endsection