@extends('layouts.frontend.index')


@section('meta_title')
    {{ \App\Helpers\Utils::getSetting('home_description') }}
@stop

@section('meta_description')
    {{ \App\Helpers\Utils::getSetting('home_description_alert') }}
@stop

@section('meta_keyword')
    {{ \App\Helpers\Utils::getSetting('home_description_alert') }}
@stop

@section('meta_image')

@stop

@section('content')

    <style>
        .home-nav .nav-item{
            min-width: 72px;
        }
        .home-nav {
            padding: 0 3px !important;
            height: 38px;
        }
        .home-nav .tile-icon {
            left: -12px !important;
            top: -20px !important;
        }
        .home-nav .nav-link:hover {
            font-weight: bold !important;
            background: none !important;
            border-color: #853208;
            font-size: 14px;
        }

        .home-nav .active-link {
            font-weight: bold !important;
            background: none !important;
            border-color: #853208;
            font-size: 14px;
            border: 0 !important;
        }

        .news-item-home {
            display: none;
        }

        #nav-right-home .active-tab {
            display: block !important;
        }
    </style>

<section class="s36">
    <div class="container pt-4 pb-3">
        <div class="row">
            <div class="col-lg-8">
                <div class="main-content">
                    <nav aria-label="breadcrumb mb-2">
                        <ol class="breadcrumb justify-content-between">
                            <li class="breadcrumb-item"><a href="https://phatgiaothanhhoa.com/#">Trang chủ</a></li>
                            <li class="breadcrumb-item active mr-auto" aria-current="page"><span>Mới cập nhật</span>
                            </li>
                            <li><small id="clockPC">Thứ bảy, 31/10/2020 | 23:19 GMT+7</small></li>
                        </ol>
                    </nav>
                    <div class="row">
                        <!--------------------------------\BEGIN CAROUSEL----------------------------------->
                        <div class="col-lg-8">
                            <div id="s36-carousel" class="carousel slide" data-ride="carousel">
                                <ol class="carousel-indicators">
                                    @if($hotNews)
                                        @foreach($hotNews as $inx => $row)
                                        <li data-target="#s36-carousel" data-slide-to="{{ $inx }}" class="{{ $inx == 1 ? 'active' : '' }}"></li>
                                        @endforeach
                                    @endif
                                </ol>
                                <div class="carousel-inner">
                                    @if($hotNews)
                                        @foreach($hotNews as $inx => $row)
                                        <div class="carousel-item {{ $inx == 1 ? 'active' : '' }}">
                                            <img class="w-100"
                                                 src="{{ \App\Helpers\Images::resize(str_replace(['/t-', '/t2-'], '/s-', $row->photo), \App\Helpers\Constant::IMAGE_SMALL_500, 357) }}"
                                                 alt="{{ $row->title }}">
                                            <div class="carousel-caption">
                                                <h5 class="mb-0">
                                                    <a title="{{ $row->title }}" href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($row->title)]) }}">
                                                        {{ $row->title }}
                                                    </a>
                                                </h5>
                                            </div>
                                        </div>
                                        @endforeach
                                    @endif
                                </div>
                            </div>
                        </div>

                        <!--------------------------------\END CAROUSEL----------------------------------->

                        <!--------------------------------\BEGIN LATEST NEWS----------------------------------->
                        <div class="col-lg-4 pl-md-0">
                            <div class="widget mt-3 mt-lg-0">
                                <div class="card ">
                                    <ul class="home-nav nav nav-tabs card-title text-center">
                                        <li class="nav-item">
                                            <a tab="latest" style="padding-left: 5px;padding-right: 5px;color: #ffd800;" class="nav-link active-link" href="javascript:;">Tin mới</a>
                                        </li>

                                        <li class="nav-item">
                                            <a tab="is_hot" style="padding-left: 5px;padding-right: 5px;color: #ffd800;" class="nav-link" href="javascript:;">Nổi bật</a>
                                        </li>

                                        <li class="nav-item">
                                            <a tab="viewed" style="padding-left: 5px;padding-right: 5px;color: #ffd800;" class="nav-link" href="javascript:;">Đọc nhiều</a>
                                        </li>

                                        <span class="tile-icon"></span>
                                    </ul>

                                    <div class="card-body" id="nav-right-home">
                                        <ul id="is_hot" class="news-item-home">
                                            @if($hotNews)
                                                @foreach($hotNews as $inx => $row)
                                                <li>
                                                    <a title="{{ $row->title }}" href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($row->title)]) }}">
                                                        {{ $row->title }}
                                                    </a>
                                                </li>
                                                @endforeach
                                            @endif
                                        </ul>

                                        <ul id="latest" class="news-item-home active-tab">
                                            @if($latestNews)
                                                @foreach($latestNews as $inx => $row)
                                                    <li>
                                                        <a title="{{ $row->title }}" href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($row->title)]) }}">
                                                            {{ $row->title }}
                                                        </a>
                                                    </li>
                                                @endforeach
                                            @endif
                                        </ul>

                                        <ul id="viewed" class="news-item-home">
                                            @if($viewedNews)
                                                @foreach($viewedNews as $inx => $row)
                                                    <li>
                                                        <a title="{{ $row->title }}" href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($row->title)]) }}">
                                                            {{ $row->title }}
                                                        </a>
                                                    </li>
                                                @endforeach
                                            @endif
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--------------------------------\END LATEST NEWS----------------------------------->
                    </div>
                    <div class="row">
                        <div class="col">
                            <div class="banner w-100 mt-3">
                                <img class="w-100"
                                     src="{{ \App\Helpers\Utils::getSetting('photo_banner_1') }}"
                                     alt="Phật giáo Thanh Hóa">
                            </div>
                        </div>
                    </div>
                    <div class="row mt-lg-3">
                        @if($hotCategories)
                            @foreach($hotCategories as $inx => $hotCategory)
                            <div class="{{ $inx == 0 ? 'col-lg-12 mb-4' : 'col-lg-6 mb-4' }}">
                                <div class="widget mt-3 mt-lg-0">
                                    <div class="card">
                                        <div class="card-title text-center">
                                            <span class="tile-icon"></span>
                                            <h5 class="text-uppercase">{{ $hotCategory->category_title }}</h5>
                                        </div>
                                        <div class="card-body">
                                            @if($hotCategory->news && !empty($hotCategory->news[0]))
                                                <div class="media mb-2">
                                                    <img class="mr-3"
                                                         src="{{ \App\Helpers\Images::resize($hotCategory->news[0]->photo, \App\Helpers\Constant::IMAGE_SMALL_140, 100) }}"
                                                         alt="{{ $hotCategory->news[0]->title }}">
                                                    <div class="media-body">
                                                        <h5 class="mt-0">
                                                            <a href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($hotCategory->news[0]->title)]) }}">{{ $hotCategory->news[0]->title }}</a>
                                                        </h5>
                                                        <div style="height: 68px;text-overflow: ellipsis;overflow: hidden;">{!! \App\Helpers\Utils::truncateStringByWord($hotCategory->news[0]->description, 80) !!}</div>
                                                    </div>
                                                </div>
                                                <ul>
                                                    @foreach($hotCategory->news as $idx => $row)
                                                        @if($inx != 0 && $idx >= 5) @break(1)  @endif
                                                        <li>
                                                            <a href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($row->title)]) }}">
                                                                {{ $row->title }}
                                                            </a>
                                                        </li>
                                                    @endforeach

                                                </ul>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        @endif

                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                @include('layouts.frontend.partial.right', [
                    'news' => \App\Models\News::where('status', 1)->where('is_focus', 1)->orderBy('id', 'DESC')->limit(\App\Helpers\Utils::getSetting('per_page_news_spotlight'))->get()
                ])
            </div>
        </div>
    </div>

    <script>
        $(function () {
            $('.home-nav a').click(function () {
                var tab = $(this).attr('tab');
                $('.home-nav a').removeClass('active-link');
                $(this).addClass('active-link');
                if(tab) {
                    $('#nav-right-home ul').removeClass('active-tab');
                    $('#' + tab).addClass('active-tab');
                }
                return false;
            });
        })
    </script>
</section>
@endsection