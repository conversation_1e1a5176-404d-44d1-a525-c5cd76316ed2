@extends('layouts.frontend.index')

@section('meta_title')
    {{ !empty($category) ? $category->category_title_seo : (!empty($category->category_title) ? $category->category_title : $request->get('km')) }}
@stop

@section('meta_description')
    {{ !empty($category) ? $category->category_description_seo : '' }}
@stop

@section('meta_keyword')
    {{ !empty($category) ? $category->category_keyword_seo : '' }}
@stop

@section('content')

<section class="achiver-phatgiao">
    <div class="container pt-4 pb-3">
        <div class="row">
            <div class="col-lg-8">
                <div class="row">

                    @include('layouts.frontend.partial.left', [
                        'categories' => $leftCategories
                    ])

                    <div class="col-md-8 order-1 order-sm-2">
                        <nav aria-label="breadcrumb mb-2">
                            <ol class="breadcrumb justify-content-between">
                                <li class="breadcrumb-item"><a href="https://phatgiaothanhhoa.com/">Trang chủ</a></li>
                                <li class="breadcrumb-item active mr-auto" aria-current="page"><span>{{ !empty($category) ? $category->category_title : 'Tìm kiếm' }}</span></li>
                                <li><small id="clockPC">Thứ bảy, 31/10/2020 | 23:20 GMT+7</small></li>
                            </ol>
                        </nav>
                        <div class="post-list" id="nextPage">


                            @if(!empty($allNews) && count($allNews) > 0)
                                @foreach($allNews as $row)
                                <div class="media mb-3">
                                    <img class="mr-0 mr-sm-3 img-fluid"
                                         src="{{ \App\Helpers\Images::resize($row->photo, \App\Helpers\Constant::IMAGE_SMALL_140, 100) }}"
                                         alt="{{ $row->title }}">
                                    <div class="media-body">
                                        <h5 class="mt-2 mt-sm-0">
                                            <a href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($row->title)]) }}">{{ $row->title }}</a>
                                        </h5>
                                        <div style="height: 68px;text-overflow: ellipsis;overflow: hidden;">{!! \App\Helpers\Utils::truncateStringByWord($row->description) !!}</div>
                                    </div>
                                </div>
                                @endforeach
                            @else
                                <div class="media mb-3"><h2>Tin đang cập nhật!</h2></div>
                            @endif


                            <div class="page text-right">
                                {!! $allNews->appends($request->input())->render();  !!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                @include('layouts.frontend.partial.right', [
                    'news' => \App\Models\News::where('status', 1)->where('is_focus', 1)->orderBy('id', 'DESC')->limit(\App\Helpers\Utils::getSetting('per_page_news_spotlight'))->get(),
                    'latestNews' => \App\Models\News::where('status', 1)->orderBy('id', 'DESC')->limit(\App\Helpers\Utils::getSetting('per_page_news_new'))->get()
                ])
            </div>
        </div>
    </div>
</section>

@endsection