@extends('layouts.frontend.index')

@section('meta_title')
    {{ !empty($news->title_seo) ? $news->title_seo : $news->title }}
@stop

@section('meta_description')
    {{ @$news->description_seo }}
@stop

@section('meta_keyword')
    {{ @$news->keyword_seo }}
@stop

@section('meta_image')
    {{ "https://phatgiaothanhhoa.com" . @$news->photo }}
@stop

@section('content')

<section class="single-phatgiao">
    <div class="container pt-3 pb-3">
        <div class="row">
            <div class="col-lg-8">
                <nav aria-label="breadcrumb mb-2">
                    <ol class="breadcrumb justify-content-between">
                        <li class="breadcrumb-item">
                            <a href="{{ url('/') }}">Trang chủ</a>
                        </li>
                        <li class="breadcrumb-item active mr-auto" aria-current="page">
                            <span>{{ $news->title }}</span>
                        </li>
                        <li>
                            <small id="clockPC">
                                {{ date("l, d/m/Y | H:i O") }}
                            </small>
                        </li>
                    </ol>
                </nav>
                <div class="sing-content">
                    <article>
                        <h1>{{ $news->title }}</h1>
                        <h4 class="text-justify excrept">
                            <em>{!! $news->description !!}</em>
                        </h4>
                        {!! $news->content  !!}
                    </article>

                    <div class="fb-share-button" data-href="http://www.facebook.com/BanTTTTPhatgiaoThanhHoa" data-layout="button" data-size="large">
                        <a target="_blank" href="https://www.facebook.com/sharer/sharer.php?u=http%3A%2F%2Fwww.facebook.com%2FBanTTTTPhatgiaoThanhHoa&amp;src=sdkpreparse" class="fb-xfbml-parse-ignore">
                            Chia sẻ
                        </a>
                    </div>

                    <div class="fb-comments" data-href="http://www.facebook.com/BanTTTTPhatgiaoThanhHoa" data-numposts="5" data-width=""></div>

                    <div class="related-posts mt-4">
                        <div class="related-posts-title">
                            <h3 class="text-uppercase">Bài viết liên quan</h3>
                            <span class="tile-icon"></span>
                        </div>
                        <div class="related-posts-list">
                            @if(!empty($otherNews))
                                @foreach($otherNews as $row)
                                <div class="media mb-2">
                                    <img class="mr-3"
                                         src="{{ \App\Helpers\Images::resize($row->photo, \App\Helpers\Constant::IMAGE_SMALL_140, 100) }}"
                                         alt="{{ $row->title }}">
                                    <div class="media-body">
                                        <h5 class="mt-2 mt-sm-0 mb-1">
                                            <a href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($row->title)]) }}">
                                                {{ $row->title }}
                                            </a>
                                        </h5>
                                        <div style="height: 68px;text-overflow: ellipsis;overflow: hidden;">{!! \App\Helpers\Utils::truncateStringByWord($row->description) !!}</div>
                                    </div>
                                </div>
                                @endforeach
                            @endif

                        </div>
                    </div>


                </div>
            </div>
            <div class="col-lg-4">
                @include('layouts.frontend.partial.right', [
                    'news' => \App\Models\News::where('status', 1)->where('is_focus', 1)->orderBy('id', 'DESC')->limit(\App\Helpers\Utils::getSetting('per_page_news_spotlight'))->get(),
                    'latestNews' => \App\Models\News::where('status', 1)->orderBy('id', 'DESC')->limit(10)->get()
                ])
            </div>
        </div>
    </div>

    <script>
        function viewedCall(objectSet) {
            $.ajax({
                url: '{{ route('news_viewed', ['news_id' => $news->id])}}',
                method: 'GET',
                data: {objectSet},
                success: function (res) {
                    localStorage.setItem("news-{{ $news->id }}", JSON.stringify(objectSet));
                }
            });
        }

        function resizeImage() {
            $('.sing-content img').each(function () {
                var widthStyle = $(this).attr('style');
                if(widthStyle) {
                    var widths = widthStyle.match(/width:([^']+)/);
                    if (widths && widths.length > 1 && widths[1] !== undefined) {
                        if (parseInt(widths[1].replace('px', '')) > 750) {
                            $(this).css({
                                width: 750,
                                height: 'auto'
                            })
                        }
                    }
                }
            })
        }

        $(function (){
            resizeImage();

            var objectSet = {value: {{ $news->id }}, timestamp: new Date().getTime()}
            var objectGet = JSON.parse(localStorage.getItem("news-{{ $news->id }}"));

            if(objectGet) {
                var dateString = parseInt(objectGet.timestamp);
                var now = new Date().getTime().toString();

                if(now - dateString > 5 * 60 * 1000) {
                    viewedCall(objectSet);
                }
            } else {
                viewedCall(objectSet);
            }
        })
    </script>
</section>

@endsection