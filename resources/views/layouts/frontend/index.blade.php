<!DOCTYPE html>
<!-- saved from url=(0029)https://phatgiaothanhhoa.com/ -->
<html lang="">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>@yield('meta_title')</title>
    <meta name="description" content="@yield('meta_description')" />
    <meta name="keyword" content="@yield('meta_keyword', \App\Helpers\Utils::getSetting('home_keyword'))">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:title" content="@yield('meta_title')">
    <meta property="og:description" content="@yield('meta_description')">
    <meta property="og:image" content="@yield('meta_image')">
    <meta property="og:image:alt" content="@yield('meta_title')">
    <link rel="icon" href="data:;base64,iVBORw0KGgo=" sizes="32x32">
    <meta property="og:locale" content="">

    <link rel="stylesheet" href="{{ asset('css/frontend/bootstrap.min.css') }}">
    <link rel="canonical" href="{{ url('/') }}">
    <link href="{{ asset('css/frontend/font-awesome/css/font-awesome.min.css') }}" rel="stylesheet" media="screen">
    <link rel="stylesheet" href="{{ asset('css/frontend/styles.css') }}">
    <script src="{{ asset('js/frontend/jquery-3.4.1.min.js') }}"></script>
    <script async defer crossorigin="anonymous" src="https://connect.facebook.net/en_US/sdk.js#xfbml=1&version=v9.0&appId=330436488158794&autoLogAppEvents=1" nonce="afsdN9YL"></script>
    <script async="" src="{{ asset('js/frontend/popper.min.js') }}"></script>
    <script async="" src="{{ asset('js/frontend/bootstrap.min.js') }}"></script>
    <script async="" src="{{ asset('js/frontend/js.min.js') }}"></script>
</head>
<body>
<div id="fb-root"></div>
@include('layouts.frontend.partial.header', [
    'user' => []
])

@yield('content')

@include('layouts.frontend.partial.footer', [
    'user' => []
])

</body>
</html>