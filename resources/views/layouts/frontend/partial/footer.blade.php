<section class="s37 mt-5">
    <footer>
        <div class="container bfooter">
            <div class="text-center">
                <h3></h3>
                <p>Bi<PERSON><PERSON> tập: ĐĐ. THÍCH NGUYÊN PHONG | Phụ trách mạng: <PERSON><PERSON> sĩ: <PERSON></p>
                <p><PERSON><PERSON><PERSON> vở đóng góp, xin gởi về Ban biên tập qua địa chỉ Email: <EMAIL></p>
                <p><PERSON><PERSON><PERSON> lạc thư tín với ĐĐ. THÍCH THANH CHÍNH xin gởi về: Chùa Đồng Lễ - P. Đông Hải - TP. Thanh Hóa/ Thanh Hó<PERSON>. <br>Điện thoại liên hệ: 0919.599.829 - 0969.968.829</p>
                <p><small>Bản quyền nội dung © 2009 - 2020, <a href="https://phatgiaothanhhoa.com/" class="text-white">Phật g<PERSON><PERSON><PERSON></a>.</small></p>
            </div>

        </div>
    </footer>
</section>
<div class="modal fade popup">
    <div class="modal-dialog modal-xl">
        <div class="modal-content"></div>
    </div>
</div>

<script src="{{ asset('js/frontend/amlich.js') }}"></script>
<script>

    $(function () {
        initCalendar();

        $('.s35 .navbar-nav a').on('click', function () {
            $('.s35 .navbar-nav').find('li.active').removeClass('active');
            $(this).parent('li').addClass('active');
        });
        $('.s35 .navbar-nav li:first').addClass('active');
        $(".s35 .navbar-nav li a").each(function () {
            if ($(this).width() > 61)
                $(this).addClass("nav-top-am");
        });

        if (!('ontouchstart' in window)) {
            $('.dropdown').on('click', function () {
                location.href = $(this).children('a.dropdown-toggle').attr('href');
            });
        }
        $('.popup').on('show.bs.modal', function (event) {
            if (typeof (event.relatedTarget) == 'object') {
                $(event.relatedTarget).addClass('spinner spinner-border');
                $.post('//phatgiaothanhhoa.com/popup', {
                    rt: $(event.relatedTarget).attr('rel'),
                    tk: '',
                    o: $(event.relatedTarget).attr('data-obj')
                }, function (d) {
                    $('.modal-content').html(d);
                    $(event.relatedTarget).removeClass('spinner spinner-border');
                });
            }
        });
        $('.dropdown').hover(function () {
            $(this).find('.collapse, .dropdown-menu').css('display', 'block');
        }).mouseleave(function () {
            $(this).find('.collapse, .dropdown-menu').css('display', 'none');
        });
    })
</script>

