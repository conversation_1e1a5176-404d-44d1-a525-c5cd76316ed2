<div class="col-md-4 order-2 order-sm-1">
    <aside class="left-sidebar">
        <ul class="navbar-nav">
            @foreach(\App\Helpers\Constant::LEFT_HEADERS as $idx => $header)
                @if($idx > 0)
                    @php $routers = \App\Helpers\Constant::routerLeftHeader() @endphp
                    <li class="nav-item dropdown border-top">
                        <a class="nav-link dropdown-toggle" data-toggle="dropdown" href="{!! $routers[$idx] !!}">{{ $header }}</a>
                        <div class="collapse navbar-collapse">
                            @if($categories)
                                @foreach($categories as $category)
                                    @if($category->left_menu == $idx)
                                    <a class="dropdown-item" href="{!! route('news', ['category_title' => \App\Helpers\Utils::slugify($category->category_title)]) !!}">{{ $category->category_title }}</a>
                                    @endif
                                @endforeach
                            @endif
                        </div>
                    </li>
                @endif
            @endforeach
        </ul>

        <div style="width: 240px;text-align: center;margin-left: 25px;padding: 0">
            @foreach(\App\Helpers\Images::getAdvertisement(\App\Helpers\Constant::ADVERTISEMENTS_LEFT) as $row)
                <div style="margin: 0;padding: 0 0 5px 0">
                    <a target="_blank" href="{{ $row->redirect_url }}" title="{{ $row->title }}">
                        <img src="{{ $row->photo }}" style="width: 100%;height: auto">
                    </a>
                </div>
            @endforeach
        </div>
    </aside>
</div>