<aside class="right-sidebar">
    <div class="widget mt-5 mt-lg-0">
        <div class="card">
            <div class="card-title text-center mb-2">
                <span class="tile-icon"></span>
                <h5 class="text-uppercase">Lịch vạn niên</h5>
            </div>
            <div class="card-body">
                <div class="mau mb-3">
                    <div class="infoCurrentDay">
                        <div class="tcle">Tháng <span class="solar_month">10</span> năm <span class="solar_year">2020</span></div>
                        <div class="solar_day">
                            <span class="number">31</span>
                            <br>
                            <span class="label">Thứ bảy</span>
                        </div>
                        <div class="lunar">
                            <div id="tongcl">
                                <span class="lunar_month">Tháng Chín</span>
                                <br>
                                <span class="lunar_day_number">15</span>
                                <br>
                                <span class="lunar_year">Năm Canh Tý</span>
                                <br>
                            </div>
                            <div id="tongc2">
                                <span>Tháng </span><span class="lunar_label_month">Bính <PERSON></span>
                                <br>
                                <span>Ngày</span> <span class="lunar_label_day">Đinh Mùi</span>
                                <br>
                                <span>Giờ</span> <span class="lunar_label_hour">Canh Tý</span>
                                <br>
                                <span class="lunar_label">Sương giáng</span>
                            </div>
                        </div>
                    </div>
                    <div class="calendar_lunar">

                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="widget mt-5">
        <div class="card ">
            <div class="card-title text-center mb-2">
                <span class="tile-icon"></span>
                <h5 class="text-uppercase">Video</h5>
            </div>
            <div class="card-body">
                <div class="media video mb-2 text-center">
                    {!! \App\Helpers\Utils::convertYoutubeToEmbed(\App\Helpers\Utils::getSetting('video')) !!}
                </div>
            </div>
        </div>
    </div>

    <div class="widget mt-5">
        @foreach(\App\Helpers\Images::getAdvertisement(\App\Helpers\Constant::ADVERTISEMENTS_RIGHT) as $row)
            <div style="margin: 0;padding: 0 0 5px 0">
                <a target="_blank" href="{{ $row->redirect_url }}" title="{{ $row->title }}">
                    <img src="{{ $row->photo }}" style="width: 100%;height: auto">
                </a>
            </div>
        @endforeach
    </div>

    @if(Route::currentRouteName() != 'home')
    <div class="widget mt-5">
        <div class="card ">
            <div class="card-title text-center mb-2">
                <span class="tile-icon"></span>
                <h5 class="text-uppercase">Tin mới nhất</h5>
            </div>
            <div class="card-body">
                @if($latestNews)
                    @foreach($latestNews as $row)
                    <div class="media mb-2">
                        <img class="mr-3" src="{{ $row->photo }}" alt="{{ $row->title }}">
                        <div class="media-body">
                            <h5 class="mt-0">
                                <a href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($row->title)]) }}">{{ $row->title }}</a>
                            </h5>
                            <div style="height: 68px;text-overflow: ellipsis;overflow: hidden;">{!! \App\Helpers\Utils::truncateStringByWord($row->description) !!}</div>
                        </div>
                    </div>
                    @endforeach
                @endif
            </div>
        </div>
    </div>
    @endif

    <div class="widget mt-5">
        <div class="card ">
            <div class="card-title text-center mb-2">
                <span class="tile-icon"></span>
                <h5 class="text-uppercase">Tiêu điểm</h5>
            </div>
            <div class="card-body">
                <div class="card-body">
                    @if($news)
                        @foreach($news as $row)
                        <div class="media mb-2">
                            <img class="mr-3"
                                 src="{{ \App\Helpers\Images::resize($row->photo, \App\Helpers\Constant::IMAGE_SMALL_140, 100) }}"
                                 alt="{{ $row->title }}">
                            <div class="media-body">
                                <h5 class="mt-0">
                                    <a href="{{ route('news_detail', ['title' => \App\Helpers\Utils::slugify($row->title)]) }}">
                                        {{ $row->title }}
                                    </a>
                                </h5>
                                <div style="height: 68px;text-overflow: ellipsis;overflow: hidden;">{!! \App\Helpers\Utils::truncateStringByWord($row->description) !!}</div>
                            </div>
                        </div>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="widget mt-5">
        <div class="card">
            <div class="card-title text-center mb-2">
                <span class="tile-icon"></span>
                <h5 class="text-uppercase">Facebook</h5>
            </div>
            <div class="card-body">
                {!! \App\Helpers\Utils::getSetting('fb_thanhhoa') !!}
            </div>
        </div>
    </div>
</aside>
