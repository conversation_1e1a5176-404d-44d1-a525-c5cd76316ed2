<!--
 * CoreUI Pro - Bootstrap 4 Admin Template
 * @version v1.0.8
 * @link http://coreui.io/pro/
 * Copyright (c) 2018 creativeLabs <PERSON><PERSON>
 * @license http://coreui.io/pro/license/
 -->
<!DOCTYPE html>
<html lang="en">

<!-- BODY options, add following classes to body to change options

// Header options
1. '.header-fixed'					- Fixed Header

// Brand options
1. '.brand-minimized'       - Minimized brand (Only symbol)

// Sidebar options
1. '.sidebar-fixed'					- Fixed Sidebar
2. '.sidebar-hidden'				- Hidden Sidebar
3. '.sidebar-off-canvas'		- Off Canvas Sidebar
4. '.sidebar-minimized'			- Minimized Sidebar (Only icons)
5. '.sidebar-compact'			  - Compact Sidebar

// Aside options
1. '.aside-menu-fixed'			- Fixed Aside Menu
2. '.aside-menu-hidden'			- Hidden Aside Menu
3. '.aside-menu-off-canvas'	- Off Canvas Aside Menu

// Breadcrumb options
1. '.breadcrumb-fixed'			- Fixed Breadcrumb

// Footer options
1. '.footer-fixed'					- Fixed footer

-->

@include('layouts.admin.partial.header', [
    'user' => []
])

<body class="app header-fixed sidebar-fixed aside-menu-fixed aside-menu-hidden">
    @include('layouts.admin.partial.navbar', [
        'user' => []
    ])

    <div class="app-body">
        @if(\Illuminate\Support\Facades\Auth::check())
            @if(\Illuminate\Support\Facades\Auth::user()->is_role == 0)
                @include('layouts.admin.partial.sidebaruser', [
                    'user' => []
                ])
            @else
                @include('layouts.admin.partial.sidebar', [
                    'user' => []
                ])
            @endif
        @endif

        @yield('content')
    </div>

    @include('layouts.admin.partial.footer', [
        'user' => []
    ])
</body>
</html>
