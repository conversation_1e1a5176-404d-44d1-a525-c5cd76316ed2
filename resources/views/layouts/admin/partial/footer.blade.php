<footer class="app-footer">
    <span><a href="http://coreui.io/pro/">Copyright</a> © {{ date('Y') }} VN.</span>
    <span class="ml-auto">Powered by <a href="http://coreui.io/pro/">Phật G<PERSON></a></span>
</footer>

<div class="modal fade" id="warningModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-primary" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">Warning Title</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="msgWarningContent">Are you sure you want to make this change?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-dismiss="modal" id="btnCancel">Cancel</button>
                <button type="button" class="btn btn-primary" id="btnAgree">OK</button>
            </div>
        </div>
        <!-- /.modal-content -->
    </div>
    <!-- /.modal-dialog -->
</div>
<!-- /.modal -->

<div id="overlay">
    <div id="text_overlay">
        <div class="sk-cube-grid">
            <div class="sk-cube sk-cube1"></div>
            <div class="sk-cube sk-cube2"></div>
            <div class="sk-cube sk-cube3"></div>
            <div class="sk-cube sk-cube4"></div>
            <div class="sk-cube sk-cube5"></div>
            <div class="sk-cube sk-cube6"></div>
            <div class="sk-cube sk-cube7"></div>
            <div class="sk-cube sk-cube8"></div>
            <div class="sk-cube sk-cube9"></div>
        </div>
        <div>Đang xử lý</div>
    </div>
</div>

<!-- Bootstrap and necessary plugins -->
<script src="{{ asset('css/admin/vendors/js/popper.min.js') }}"></script>
<script src="{{ asset('css/admin/vendors/js/bootstrap.min.js') }}"></script>
<script src="{{ asset('css/admin/vendors/js/pace.min.js') }}"></script>

<!-- Plugins and scripts required by all views -->
<script src="{{ asset('css/admin/vendors/js/Chart.min.js') }}"></script>

<!-- CoreUI Pro main scripts -->
<script src="{{ asset('js/admin/app.js') }}"></script>

<!-- Plugins and scripts required by this views -->
<script src="{{ asset('css/admin/vendors/js/toastr.min.js') }}"></script>
<script src="{{ asset('css/admin/vendors/js/gauge.min.js') }}"></script>
<script src="{{ asset('css/admin/vendors/js/moment.min.js') }}"></script>

<script src="{{ asset('css/admin/vendors/js/jquery.validate.min.js') }}"></script>
<script src="{{ asset('css/admin/vendors/js/daterangepicker.min.js') }}?t=12"></script>

<!-- Custom scripts required by this view -->
<script src="{{ asset('js/admin/views/main.js') }}"></script>
<script src="{{ asset('js/lodash.js') }}"></script>
<script src="{{ asset('js/cleave.min.js') }}"></script>
<script src="{{ asset('js/admin/jquery-confirm.min.js') }}"></script>
<script src="{{ asset('js/admin/views/popovers.js') }}"></script>
<script src="{{ asset('js/admin/jquery.tagsinput-revisited.js') }}"></script>
@yield('extra-scripts')
<script>
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    $(document).ajaxStart(function(){
        onLoading();
    }).ajaxStop(function(){
        offLoading()
    });

    moment.locale('vn');

    function onLoading() {
        document.getElementById("overlay").style.display = "block";
    }

    function offLoading() {
        document.getElementById("overlay").style.display = "none";
    }

    jQuery('input.datepicker').daterangepicker({
        opens: 'right',
        singleDatePicker: true,
        autoUpdateInput: false,
        locale: {
            format: 'DD/MM/YYYY',
            "fromLabel": "Từ",
            "toLabel": "Đến",
            "customRangeLabel": "Lựa chọn",
            "daysOfWeek": [
                "CN",
                "T2",
                "T3",
                "T4",
                "T5",
                "T6",
                "T7"
            ],
            "monthNames": [
                "Tháng Một",
                "Tháng Hai",
                "Tháng Ba",
                "Tháng Bốn",
                "Tháng Năm",
                "Tháng Sáu",
                "Tháng Bảy",
                "Tháng Tám",
                "Tháng Chín",
                "Tháng Mười",
                "Tháng Mười một",
                "Tháng Mười hai"
            ],
            cancelLabel: 'Bỏ qua',
            applyLabel: 'Chọn'
        }
    }, function(start, end, label) {
        var years = moment().diff(start, 'years');
    });
    jQuery('input.datepicker').on('apply.daterangepicker', function(ev, picker) {
        jQuery(this).val(picker.startDate.format('DD/MM/YYYY'));
    });
    jQuery('input.datepicker').on('cancel.daterangepicker', function(ev, picker) {
        jQuery(this).val('');
    });

    jQuery('input.datefilter').daterangepicker({
        opens: 'left',
        autoUpdateInput: false,
        locale: {
            format: 'DD/MM/YYYY',
            "fromLabel": "Từ",
            "toLabel": "Đến",
            "customRangeLabel": "Lựa chọn",
            "daysOfWeek": [
                "CN",
                "T2",
                "T3",
                "T4",
                "T5",
                "T6",
                "T7"
            ],
            "monthNames": [
                "Tháng Một",
                "Tháng Hai",
                "Tháng Ba",
                "Tháng Bốn",
                "Tháng Năm",
                "Tháng Sáu",
                "Tháng Bảy",
                "Tháng Tám",
                "Tháng Chín",
                "Tháng Mười",
                "Tháng Mười một",
                "Tháng Mười hai"
            ],
            cancelLabel: 'Bỏ qua',
            applyLabel: 'Chọn'
        }
    }, function(start, end, label) {

    });

    jQuery('input.datefilter').on('apply.daterangepicker', function(ev, picker) {
        jQuery(this).val(picker.startDate.format('DD/MM/YYYY') + ' - ' + picker.endDate.format('DD/MM/YYYY'));
    });
    jQuery('input.datefilter').on('cancel.daterangepicker', function(ev, picker) {
        jQuery(this).val('');
    });

    jQuery('input.datetimefilter').daterangepicker({
        opens: 'left',
        autoUpdateInput: false,
        timePicker: true,
        startDate: moment().startOf('day'),
        endDate: moment().startOf('day').add(23, 'hour'),
        locale: {
            format: 'DD/MM/YYYY',
            "fromLabel": "Từ",
            "toLabel": "Đến",
            "customRangeLabel": "Lựa chọn",
            "daysOfWeek": [
                "CN",
                "T2",
                "T3",
                "T4",
                "T5",
                "T6",
                "T7"
            ],
            "monthNames": [
                "Tháng Một",
                "Tháng Hai",
                "Tháng Ba",
                "Tháng Bốn",
                "Tháng Năm",
                "Tháng Sáu",
                "Tháng Bảy",
                "Tháng Tám",
                "Tháng Chín",
                "Tháng Mười",
                "Tháng Mười một",
                "Tháng Mười hai"
            ],
            cancelLabel: 'Bỏ qua',
            applyLabel: 'Chọn'
        },
    }, function(start, end, label) {

    });
    jQuery('input.datetimefilter').on('apply.daterangepicker', function(ev, picker) {
        jQuery(this).val(picker.startDate.format('DD/MM/YYYY hh:mm A') + ' - ' + picker.endDate.format('DD/MM/YYYY hh:mm A'));
    });
    jQuery('input.datetimefilter').on('cancel.daterangepicker', function(ev, picker) {
        jQuery(this).val('');
    });

    const SystemScript = {
        postAjax: function (url, data, callback) {
            $.ajax({
                url: url,
                method: 'POST',
                data: data,
                success: function (res) {
                    if (callback) {
                        callback(res);
                    }
                }
            });
        },
        postAjaxFormData: function (url, data, callback) {
            $.ajax({
                url: url,
                method: 'POST',
                processData: false,
                contentType: false,
                data: data,
                success: function (res) {
                    if (callback) {
                        callback(res);
                    }
                }
            });
        },
        getAjax: function (url, data, callback) {
            $.ajax({
                url: url,
                method: 'GET',
                data: data,
                success: function (res) {
                    if (callback) {
                        callback(res);
                    }
                }
            });
        }
    }
</script>
