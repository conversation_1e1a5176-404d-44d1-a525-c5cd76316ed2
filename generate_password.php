<?php

// <PERSON><PERSON>t để sinh ra password hash cho <PERSON>
require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\Hash;

// Password cần hash
$password = 'Son@1593';

// Tạo hash password sử dụng bcrypt (mặc định của <PERSON>)
$hashedPassword = password_hash($password, PASSWORD_BCRYPT);

echo "Password gốc: " . $password . "\n";
echo "Password đã hash: " . $hashedPassword . "\n";
echo "\n";
echo "Bạn có thể copy chuỗi hash này và update trực tiếp vào database:\n";
echo "UPDATE users SET password = '{$hashedPassword}' WHERE email = '<EMAIL>';\n";
