<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreatePagesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('pages', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->bigInteger('category_id')->default(0);
			$table->string('title', 500)->comment('JSON data');
			$table->string('title_seo', 500)->nullable()->comment('JSON data');
			$table->string('description_seo', 500)->nullable()->comment('JSON data');
			$table->string('keyword_seo', 500)->nullable()->comment('JSON data');
			$table->text('description', 65535)->nullable()->comment('JSON data');
			$table->text('content')->comment('JSON data');
			$table->boolean('status')->default(1)->comment('0: is disabled');
			$table->string('photo')->nullable();
			$table->string('slugify')->nullable();
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('pages');
	}

}
