<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateUsersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('users', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('full_name');
			$table->string('user_name')->nullable();
			$table->string('avatar', 300)->nullable();
			$table->string('address')->nullable();
			$table->string('country')->nullable();
			$table->string('district')->nullable();
			$table->string('email')->unique();
			$table->dateTime('email_verified_at')->nullable();
			$table->string('password');
			$table->string('phone')->nullable();
			$table->string('mobile')->nullable();
			$table->boolean('is_active')->default(0);
			$table->string('image_security', 300)->nullable();
			$table->integer('level')->default(0)->comment('user VIP level: 0, 1,2,3,4,5');
			$table->integer('is_role')->default(0)->comment('0: user, 1: staff; 2: admin');
			$table->string('remember_token', 100)->nullable();
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('users');
	}

}
