<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateNewsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('news', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->bigInteger('category_id')->default(0);
			$table->string('title', 500)->comment('JSON data');
			$table->string('title_seo', 500)->nullable()->comment('JSON data');
			$table->string('description_seo', 500)->nullable()->comment('JSON data');
			$table->string('keyword_seo', 500)->nullable()->comment('JSON data');
			$table->text('description', 65535)->nullable()->comment('JSON data');
			$table->text('content')->comment('JSON data');
			$table->boolean('status')->default(1)->comment('0: is disabled');
			$table->boolean('is_hot')->comment('1: add to slide show');
			$table->smallInteger('is_focus')->nullable()->default(0);
			$table->string('photo')->nullable();
			$table->dateTime('date_public')->nullable();
			$table->dateTime('date_end')->nullable();
			$table->string('author')->nullable()->default('Ban biên tập nhà chùa')->index('author');
			$table->string('video_url')->nullable()->default('youtube hoặc vimeo');
			$table->string('source_url')->nullable()->default('Link bài viết gốc');
			$table->text('tags', 65535)->nullable()->comment('JSON data');
			$table->string('slugify')->nullable();
			$table->integer('viewed')->nullable()->default(1);
			$table->timestamps();
			//$table->index(['title','content'], 'title');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('news');
	}

}
