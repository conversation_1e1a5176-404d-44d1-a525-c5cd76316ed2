<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateAdvertisementsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('advertisements', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('title', 255);
            $table->string('photo', 255);
            $table->string('redirect_url', 255)->nullable(true);
            $table->smallInteger('status')->default(1);
            $table->smallInteger('position')->default(1)->comment('1: Rightt, 2: Left, 3: Home center');
            $table->integer('sort')->default(1);
            $table->integer('total_click')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('advertisements');
    }
}
