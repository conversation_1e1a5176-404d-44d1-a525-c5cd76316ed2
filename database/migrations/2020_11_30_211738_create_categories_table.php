<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateCategoriesTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('categories', function(Blueprint $table)
		{
			$table->bigInteger('id', true)->unsigned();
			$table->string('category_title', 500)->comment('JSON data');
			$table->boolean('category_level')->default(1);
			$table->text('category_description', 65535)->nullable();
			$table->string('category_title_seo', 500)->nullable();
			$table->string('category_description_seo', 500)->nullable();
			$table->string('category_keyword_seo', 500)->nullable();
			$table->string('redirect_link')->nullable();
			$table->integer('parent_id')->default(0)->comment('0: is parent');
			$table->boolean('status')->default(1)->comment('0: is disabled');
			$table->boolean('hot')->default(0)->comment('0: is normal');
			$table->boolean('menu_top')->default(0)->comment('1: is on');
			$table->string('photo')->nullable();
			$table->integer('sort')->default(0);
			$table->string('slugify')->nullable();
			$table->smallInteger('left_menu')->nullable()->default(0);
			$table->timestamps();
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('categories');
	}

}
