<?php

use Illuminate\Database\Seeder;

class SettingsTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $arrayData = [
            # Mi<PERSON>u tả trang chủ Website (hiển thị khi người dùng gõ tìm kiếm! - Rất quan trọng)
            ['key' => 'home_description', 'values' => 'Phật giáo Thanh Hoá'],
            # Miêu tả trang chủ Website (hiển thị khi người dùng gõ tìm kiếm!)
            ['key' => 'home_description_alert', 'values' => 'Chào mừng quý đạo hữu đến với Website chính thức của Ban Trị sự Giáo hội Phật giáo Việt Nam tỉnh Thanh Hóa'],
            # Từ khóa cho trang chủ (Hữu ích cho Google nhận diện tìm kiếm)
            ['key' => 'home_keyword', 'values' => 'tin tức, tin phật gi<PERSON><PERSON>, ph<PERSON><PERSON> gi<PERSON><PERSON>, <PERSON><PERSON> hóa, b<PERSON><PERSON> phật gi<PERSON>, <PERSON><PERSON>'],
            # Tên thương hiệu Website!
            ['key' => 'website_branch', 'values' => 'Phật giáo Thanh Hoá'],
            # Hình ảnh hiển thị mặc định cho bài viết không có ảnh!
            ['key' => 'default_logo', 'values' => 'https://cdn.vnbcdn.net/vinabeach.vn/album/file/s-logo-1573608803.png'],
            # URL fanpage facebook!
            ['key' => 'fb_thanhhoa', 'values' => 'https://www.facebook.com/PG.ThanhHoa/'],
            # Tiêu đề cho trang tin mới!
            ['key' => 'new_title_news', 'values' => 'Tin mới cập nhật'],
            # Miêu tả cho trang tin mới!
            ['key' => 'new_description_news', 'values' => ''],
            # Tiêu đề cho trang nổi bật.
            ['key' => 'hot_news', 'values' => 'Tin tức nổi bật vừ qua'],
            # Miêu tả cho trang nổi bật.
            ['key' => 'hot_description_news', 'values' => ''],
            # Từ khóa cho trang nổi bật.
            ['key' => 'hot_keyword_news', 'values' => ''],
            # Cài đặt số lượng hiển thị tin tiêu điểm.
            ['key' => 'per_page_news_spotlight', 'values' => '10'],
            # Cài đặt số lượng hiển thị tin mới tại trang chủ.
            ['key' => 'per_page_news_new', 'values' => '5'],
            # Banner: nhập đường dẫn ảnh banner 1
            ['key' => 'photo_banner_1', 'values' => '/images/settings/s-banner-dai-le-phat-dan-1587560989.jpg'],
            #Banner: nhập đường dẫn ảnh banner 2
            ['key' => 'photo_banner_2', 'values' => ''],
            # Cột 2 (bên phải): nhập đường dẫn video
            ['key' => 'video', 'values' => 'https://www.youtube.com/watch?v=Ab234U3BL_w&amp;t=511s'],
            # Miêu tả
            ['key' => 'number_des', 'values' => '1'],
            # Cài đặt số lượng tin hiển thị cho danh mục trên trang.
            ['key' => 'per_page_category', 'values' => '20'],
            # Cài đặt số lượng hiển thị tin liên quan.
            ['key' => 'per_page_news_related', 'values' => '6'],
            # Other
            ['key' => 'other', 'values' => ''],
        ];

        foreach ($arrayData as $array) {
            \App\Models\Setting::create([
                'key' => $array['key'],
                'values' => $array['values'],
            ]);
        }

    }
}
