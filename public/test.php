<?php
ini_set('display_errors',1);
class curl {

    var $pages = array(
        "login" => "https://vital4u.net/agency/index.php?main=Admin_login",
        "online_order"=>"https://vital4u.net/agency/index.php?main=Admin_onlineorder&cmd=add"
    );
    var $ch = null;
    var $options = array();

    function __construct() {
        $this->ch = curl_init();
        $this->options = [
            CURLOPT_HEADER => FALSE,
            CURLOPT_RETURNTRANSFER => TRUE,
            CURLOPT_COOKIEFILE => '/tmp/cookie.txt',
            CURLOPT_USERAGENT => $_SERVER['HTTP_USER_AGENT'],
            CURLOPT_FOLLOWLOCATION=>true,
            CURLOPT_COOKIEJAR => '/tmp/cookies.txt'
        ];
        curl_setopt_array($this->ch, $this->options);
    }

    function __destruct() {
        $this->close();
    }

    function close() {
        curl_close($this->ch);
    }


    function login() {

        $this->options[CURLOPT_URL] = $this->pages['login'];
        $this->options[CURLOPT_POST] = true;
        $this->options[CURLOPT_POSTFIELDS] = array("user"=>"transon","pass"=>"1234567890","form_block"=>"Admin_login");
        curl_setopt_array($this->ch, $this->options);
        $result=curl_exec($this->ch);
        //print $result;
    }

    function onlineOrder() {

        $this->options[CURLOPT_URL] = $this->pages['online_order'];
        $this->options[CURLOPT_POST] = false;
        curl_setopt_array($this->ch, $this->options);
        $result=curl_exec($this->ch);
        print $result;
    }
}
$curl = new curl();
$curl->login();
$curl->onlineOrder();
