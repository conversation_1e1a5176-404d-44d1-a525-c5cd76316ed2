CKEDITOR.plugins.setLang('youtube', 'hu', {
	button : 'Youtube videó beille<PERSON>té<PERSON>',
	title : 'Youtube videó beillesztése',
	txtEmbed : 'Illessze be a beágyazott kódot',
	txtUrl : 'Illessze be a Youtube videó URL-jét',
	txtWidth : 'Szélesség',
	txtHeight : 'Magasság',
	txtStartAt : '<PERSON><PERSON><PERSON><PERSON> id<PERSON>pont (ss vagy mm:ss vagy hh:mm:ss)',
	chkRelated : 'Aj<PERSON><PERSON>t videók megjeleníté<PERSON>, amikor a videó befejeződik',
	chkPrivacy : 'Fokozott adatvédelmi mód engedélyezése',
	chkOlderCode : '<PERSON><PERSON>gi beágyazott kód használata',
	chkAutoplay : 'Automatikus lejátszás',
	chkControls : '<PERSON>j<PERSON><PERSON>zásvezérlők mutatása',
	noCode : 'A beá<PERSON> kód, vagy az URL megadása kötelező',
	invalidEmbed : 'A beágyazott kód érvénytelen',
	invalidUrl : 'A megadott URL érvénytelen',
	or : 'vagy',
	noWidth : 'A szélesség megadása kötelező',
	invalidWidth : 'Érvényes szélességet adjon meg',
	noHeight : 'A magasság megadása kötelező',
	invalidHeight : 'Érvényes magasságot adjon meg',
	invalidTime : 'Érvényes kezdő időpontot adjon meg',
	txtResponsive : 'Reszponzív videó',
	txtNoEmbed : 'Csak kép és hivatkozás jelenjen meg'
});
