 var common={clock:function(){var d=new Date;var weekday=new Array(7);weekday[0]="Ch\u1ee7 nh\u1eadt";weekday[1]="Th\u1ee9 hai";weekday[2]="Th\u1ee9 ba";weekday[3]="Th\u1ee9 t\u01b0";weekday[4]="Th\u1ee9 n\u0103m";weekday[5]="Th\u1ee9 s\u00e1u";weekday[6]="Th\u1ee9 b\u1ea3y";var n=weekday[d.getDay()];var now=new Date;var date=now.getDate();var month=now.getMonth()+1;var hour=now.getHours();var minute=now.getMinutes();var outStr=n+", "+date+"/"+month+"/"+now.getFullYear()+" | "+(hour>=10?hour:"0"+hour)+":"+(minute>=10?minute:"0"+minute)+" GMT+7";$("#clockPC").html(outStr);setTimeout("common.clock()",1E3)}}; function isNumberKey(obj,evt,d,md) { var charCode = (evt.which) ? evt.which : event.keyCode; if (charCode == 46 && obj.value.split('.').length > 1 && d) return false; if ((md || d ) && charCode != 46 && charCode > 31 && (charCode < 48 || charCode > 57))return false; if ((!md && !d ) && charCode > 31 && (charCode < 48 || charCode > 57))return false; return true; };common.clock(); $.fn._p = function(u,rts,rtc,l,w,p,tk,lm){ $('.page .selected a').find('span').addClass('spinner-border spinner-border-sm'); $('#'+l).load(u,{rt:'nextPage',rtc:rtc,rts:rts,p:p,lm:lm,tk:tk},function(d){}); }; function _cv(c){ var forms = document.getElementsByClassName(c),va = 0; var v = Array.prototype.filter.call(forms, function(form) { form.classList.add('was-validated'); if (form.checkValidity() === false){ event.preventDefault(); event.stopPropagation(); }else va = 1; }); return Boolean(va); } $.fn._rsm = function(id,u,p){ var b=true,v = _cv(id);  if(v&&b){ $('#l'+id).attr('rel',$('#l'+id).attr('class')).attr('class','spinner-border spinner-border-sm'); $.ajax({ url:u, type:'post', data:$('#'+id).serialize(), dataType: 'json' }).done(function(rt){ if(!rt.err){ $('#l'+id).attr('class','fa fa-check'); $('#'+id).removeClass('was-validated').trigger('reset'); $('.s'+id).html(''); }else{ $('#l'+id).attr('class',$('#l'+id).attr('rel')); } if(p){ $('.modal-content').html('<div class=\"alert alert-success m-0\">'+rt.msg+'</div>'); $('.popup').modal(); }else{ $('#e'+id).html(rt.msg); } }).fail(function() { $('#l'+id).attr('class','fa fa-times text-danger'); }); } }