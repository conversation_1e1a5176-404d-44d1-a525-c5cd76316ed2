a {
    color: #333
}

article {
    text-align: justify
}

article img {
    max-width: 100%;
    margin: 0 auto;
    display: block
}

h1 {
    font-size: 2em;
    margin-top: 0.67em;
    margin-bottom: 0.67em
}

h2 {
    font-size: 1.5em;
    margin-top: 0.83em;
    margin-bottom: 0.83em
}

h3 {
    font-size: 1.17em;
    margin-top: 1em;
    margin-bottom: 1em
}

h4 {
    font-size: 1em;
    margin-top: 1.33em;
    margin-bottom: 1.33em
}

h5 {
    font-size: 0.83em;
    margin-top: 1.67em;
    margin-bottom: 1.67em
}

h6 {
    font-size: 0.67em;
    margin-top: 2.33em;
    margin-bottom: 2.33em
}
.pagination .active > span.page-link {
    padding: 8px 14px;
}

.page {
    padding: 5px;
    position: relative
}

.page a {
    border: 1px solid #CCC;
    display: inline-block;
    line-height: 30px;
    margin: 0;
    padding: 9px 14px;
}

.page a:hover {
    background-color: #EEE
}

.page .selected {
    display: inline-block;
    position: relative
}

.page .selected a {
    background-color: #EEE;
    color: #F00
}

.page .dropdown-menu {
    float: none;
    left: auto;
    margin: 0;
    padding: 0;
    right: 0;
    width: 100%
}

body {
    background: #ffdea1 url(/images/s-bg-middle-1573550661.jpg) no-repeat scroll center top
}

@media (min-width: 1200px) {
    .container {
        max-width: 1170px
    }
}

.s35 .btop {
    background: #340100 url(/images/s-bg-top-1573550617.jpg) repeat scroll center center;
    color: #ffd800;
    border-bottom: 3px solid #ffd800
}

.s35 h3 {
    font-size: 22px;
    margin-top: 20px;
    margin-bottom: 5px
}

.s35 #timkiem {
    background: transparent url(/images/s-navi-1573550550.png) no-repeat scroll 0 1px;
    height: 55px;
    padding: 7px 10px 0;
    width: 280px;
    margin: 20px auto 0;
    position: relative
}

.s35 #km {
    background: 0 0;
    border: none;
    width: 212px;
    font-size: 13px;
    vertical-align: 2px
}

.s35 #tmkiem {
    background: 0 0;
    border: none;
    cursor: pointer;
    height: 23px;
    width: 34px;
    position: absolute;
    right: 13px
}

@media (max-width: 560px) {
    .s35 h3 {
        font-size: 18px
    }
}

.s35 .main-menu .container {
    background-color: #fff
}

.s35 .main-menu ul li a {
    color: #853208
}

@media (min-width: 992px) {
    .s35 .navbar {
        padding-top: 0
    }

    .s35 .main-menu ul li a.nav-link {
        font-size: 13px;
        display: block;
        padding: 17px 15px 34px;
        text-align: center;
        height: 71px;
        min-width: 86px;
        font-weight: 700
    }

    .s35 .main-menu ul li.active a, .s35 .main-menu ul li:hover a {
        background: rgba(0, 0, 0, 0) url(/images/s-navi-1573550261.png) repeat scroll 8px 0;
        color: #ffd800;
        text-decoration: none
    }

    .s35 .main-menu ul li a.nav-top-am {
        background-position: 0 -79px;
        width: 112px
    }

    .s35 .main-menu ul li a.nav-top-am {
        background-position: 0 -79px;
        width: 112px
    }
}

@media (max-width: 768px) {
    .s35 .navbar {
        background: url(/images/s-bg-box-1573550840.jpg) repeat scroll center center
    }

    .s35 .main-menu .navbar-brand {
        font-size: 16px;
        color: #ffd800;
        text-transform: uppercase
    }

    .s35 .navbar-toggler {
        color: #ffd800;
        font-size: 24px;
        outline: 0
    }

    .s35 h3 {
        font-size: 18px
    }

    .s35 .main-menu ul li a.nav-link {
        color: #ffd800;
        padding-left: 10px;
        border-bottom: 1px dotted #ffd800
    }

    .s35 .main-menu ul li a.nav-link::before {
        content: '\f0da';
        font-family: fontawesome;
        margin-right: 5px
    }

    .s35 .main-menu ul li:last-child a.nav-link {
        border-bottom: none
    }

    .s35 .main-menu ul li.active a.nav-link, .s35 .main-menu ul li:hover a.nav-link {
        color: #fff;
        background: #6f2907;
        border-color: #6f2907
    }
}

.s36 {
    margin-top: 1px
}

.s36 .container {
    border-radius: 0 0 7px 7px
}

.s36 a {
    line-height: 18px;
    color: #853208;
    font-size: 14px;
    display: inline-block;
    text-decoration: none
}

.s36 .container {
    background-color: #fff;
    box-shadow: 0 10px 10px #888
}

.s36 a:hover {
    color: #d6990c
}

.s36 .breadcrumb {
    padding: 8px 15px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border-radius: 4px
}

.s36 .breadcrumb::after, .s36 .breadcrumb::before {
    display: none
}

.s36 .slide img {
    height: 325px
}

.s36 .slide .carousel-caption {
    background: rgba(63, 2, 3, .6);
    width: 100%;
    right: 0;
    left: 0;
    bottom: 30px;
    padding: 10px 15px
}

.s36 .slide .carousel-caption a {
    color: #ffd800;
    font-size: 16px;
    text-decoration: none
}

.s36 .slide .carousel-caption:hover a {
    color: #fff
}

.s36 .carousel-indicators {
    margin-bottom: 0
}

.s36 .carousel-indicators li {
    width: 11px;
    height: 11px;
    border-radius: 100%
}

.s36 .carousel-indicators li.active {
    background-color: #853208;
    opacity: 1
}

.s36 .widget .card {
    border-radius: 7px;
    border-color: #964609
}

.s36 .widget li a {
    width: 100%
}

.s36 .widget .card-title {
    border-radius: 7px 7px 0 0;
    background: url(/images/s-bg-box-1573550840.jpg) repeat scroll center center;
    padding: 10px 0;
    position: relative;
    margin-left: -1px;
    margin-right: -1px;
    margin-bottom: 0
}

.s36 .widget .card-title h5 {
    color: #ffd800;
    font-size: 18px;
    margin-bottom: 0
}

.s36 .widget .card-title .tile-icon {
    display: inline-block;
    position: absolute;
    height: 42px;
    left: -10px;
    position: absolute;
    top: 0;
    width: 50px;
    background: url(/images/s-icontt-1573552480.png) no-repeat scroll -14px -18px
}

.s36 .widget .card-body {
    padding: 5px 7px;
    min-height: 270px
}

.s36 .widget ul {
    list-style: none;
    padding-left: 0;
    margin: 0
}

.s36 .widget ul li a {
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 1px dotted #853208
}

.s36 .widget ul li:last-child a {
    border-bottom: none;
    padding-bottom: 0
}

.s36 .widget.widget-2col ul li:last-child a {
    padding-bottom: 5px;
    border-bottom: 1px dotted #853208
}

.s36 .main-content .widget .media {
    background: #eee;
    margin-left: -7px;
    margin-right: -7px;
    margin-top: -5px;
    padding: 10px 7px 0 7px
}

.s36 .widget ul li a:before {
    content: '\f0da';
    font-family: fontawesome;
    margin-right: 5px
}

.s36 .widget .media img {
    width: 110px;
    height: 80px
}

.s36 .widget .media.video {
    display: block
}

.s36 .widget .media h5 {
    line-height: 1;
    margin-bottom: 3px
}

.s36 .widget .media h5 a {
    font-weight: 600;
    font-size: 15px
}

.s36 .widget .media p, .s36 .widget .media div {
    font-size: 14px;
    line-height: 1.3
}

.s36 aside .widget .card-title .tile-icon {
    left: 10%;
    top: -50%;
    background-position-y: -547px;
    background-position-x: -3px
}

.s36 aside .widget .mau {
    margin-top: 10px;
    clear: both
}

.s36 aside .widget .infoCurrentDay {
    background: #8b4b0b url(/images/s-lich-1573552517.png) repeat-x right center;
    color: #fd0;
    border-radius: 10px 10px 0 0;
    -moz-border-radius: 10px 10px 0 0
}

.s36 aside .widget .tcle {
    text-align: center;
    font-size: 13px;
    line-height: 32px
}

.s36 aside .widget .solar_day {
    text-align: center
}

.s36 aside .widget .solar_day .number {
    text-align: center;
    font-family: Myriad Pro;
    font-size: 64pt;
    font-weight: 700;
    line-height: 67px
}

.s36 aside .widget .label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em
}

.s36 aside .widget #tongcl {
    float: left;
    width: 161px;
    text-align: center
}

.s36 aside .widget #tongc2 {
    text-align: center
}

.widget .calendar_lunar {
    border-radius: 0 0 10px 10px;
    -moz-border-radius: 0 0 10px 10px;
    /*padding: 0 1px 1px 1px;*/
    background: #82400a
}

.widget .calendar_lunar table {
    color: #743309;
    width: 100%;
    background: #fff;
    border: 1px solid #743309;
    border-collapse: collapse
}

.widget .calendar_lunar table tr td {
    height: 20px !important
}

.widget .ngaytuan {
    font-weight: 700;
    color: #fd0;
    text-align: center;
    font-size: 17px;
    background: #83410a
}

.widget .ngaythang, .widget .homnay {
    /*border: 1px solid #bfbfbf;*/
    color: #743309;
    font-size: 13px;
    width: 14%;
    padding: 2px;
}

aside .widget .am {
    font-weight: 500;
    font-size: 9px;
    color: #777;
    line-height: 8px;
    text-align: right
}

aside .widget .navi-l, .s36 aside .widget .navi-r, .s36 aside .widget .tenthang {
    text-align: center
}

h5 {
    margin-top: 0;
    font-weight: 400
}

.s36 .clock-pc {
    padding-left: 0
}

.s36 .clock-pc::before {
    display: none
}

@media (min-width: 768px) {
    .s36 .clock-pc {
        position: absolute;
        right: 35px
    }
}

.s36 .breadcrumb-item span {
    font-size: 12px;
    line-heigh: 24px
}

.s36 .carousel {
    position: relative;
    border: 2px solid #853208;
    border-radius: 7px
}

.s36 .carousel img {
    border-radius: 5px
}

.s36 .banner img {
    border-radius: 7px
}

.s37 {
    background: #732e09 url(/images/s-bg-footer-1573553024.png) repeat-x left top scroll;
    color: #ffeac2
}

.s37 footer {
    padding: 15px 0
}

.s37 footer p {
    margin-bottom: 0
}

.s37 footer h3 {
    font-size: 22px
}

.achiver-phatgiao {
    margin-top: 1px
}

.achiver-phatgiao .container {
    border-radius: 0 0 7px 7px
}

.achiver-phatgiao a {
    line-height: 18px;
    color: #853208;
    font-size: 14px;
    display: inline-block;
    text-decoration: none
}

.achiver-phatgiao .container {
    background-color: #fff;
    box-shadow: 0 10px 10px #888
}

.achiver-phatgiao a:hover {
    color: #d6990c
}

.achiver-phatgiao .breadcrumb {
    padding: 8px 15px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border-radius: 4px
}

.achiver-phatgiao .widget .card {
    border-radius: 7px;
    border-color: #964609
}

.achiver-phatgiao .widget li a {
    width: 100%
}

.achiver-phatgiao .widget .card-title {
    border-radius: 7px 7px 0 0;
    background: url(/images/s-bg-box-1573550840.jpg) repeat scroll center center;
    padding: 10px 0;
    position: relative;
    margin-left: -1px;
    margin-right: -1px;
    margin-bottom: 0
}

.achiver-phatgiao .widget .card-title h5 {
    color: #ffd800;
    font-size: 18px;
    margin-bottom: 0
}

.achiver-phatgiao .widget .card-title .tile-icon {
    display: inline-block;
    position: absolute;
    height: 42px;
    left: -10px;
    position: absolute;
    top: 0;
    width: 50px;
    background: url(/images/s-icontt-1573552480.png) no-repeat scroll -14px -18px
}

.achiver-phatgiao .widget .card-body {
    padding: 5px 7px
}

.achiver-phatgiao .widget ul {
    list-style: none;
    padding-left: 0;
    margin: 0
}

.achiver-phatgiao .widget ul li a {
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 1px dotted #853208
}

.achiver-phatgiao .widget ul li:last-child a {
    border-bottom: none;
    padding-bottom: 0
}

.achiver-phatgiao .widget.widget-2col ul li:last-child a {
    padding-bottom: 5px;
    border-bottom: 1px dotted #853208
}

.achiver-phatgiao .main-content .widget .media {
    background: #eee;
    margin-left: -7px;
    margin-right: -7px;
    margin-top: -5px;
    padding: 10px 7px 0 7px
}

.achiver-phatgiao .widget ul li a:before {
    content: '\f0da';
    font-family: fontawesome;
    margin-right: 5px
}

.achiver-phatgiao .widget .media img {
    width: 110px;
    height: auto;
    min-height: 80px
}

.achiver-phatgiao .widget .media.video {
    display: block
}

.achiver-phatgiao .widget .media h5 {
    line-height: 1;
    margin-bottom: 3px
}

.achiver-phatgiao .widget .media h5 a {
    font-weight: 600;
    font-size: 15px
}

.achiver-phatgiao .widget .media p {
    font-size: 14px;
    line-height: 1.3
}

.achiver-phatgiao aside .widget .card-title .tile-icon {
    left: 10%;
    top: -50%;
    background-position-y: -547px;
    background-position-x: -3px
}

.achiver-phatgiao aside .widget .mau {
    margin-top: 10px;
    clear: both
}

.achiver-phatgiao aside .widget .infoCurrentDay {
    background: #8b4b0b url(/images/s-lich-1573552517.png) repeat-x right center;
    color: #fd0;
    border-radius: 10px 10px 0 0;
    -moz-border-radius: 10px 10px 0 0
}

.achiver-phatgiao aside .widget .tcle {
    text-align: center;
    font-size: 13px;
    line-height: 32px
}

.achiver-phatgiao aside .widget .solar_day {
    text-align: center
}

.achiver-phatgiao aside .widget .solar_day .number {
    text-align: center;
    font-family: Myriad Pro;
    font-size: 64pt;
    font-weight: 700;
    line-height: 67px
}

.achiver-phatgiao aside .widget .label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em
}

.achiver-phatgiao aside .widget #tongcl {
    float: left;
    width: 161px;
    text-align: center
}

.achiver-phatgiao aside .widget #tongc2 {
    text-align: center
}

.achiver-phatgiao aside .widget .am {
    font-weight: 500;
    font-size: 9px;
    color: #777;
    line-height: 8px;
    text-align: right
}

.achiver-phatgiao aside .widget .navi-l, .achiver-phatgiao aside .widget .navi-r, .achiver-phatgiao aside .widget .tenthang {
    text-align: center
}

.achiver-phatgiao .left-sidebar ul {
    padding-left: 0
}

.achiver-phatgiao .left-sidebar ul li a {
    display: inline-block;
    width: 100%
}

.achiver-phatgiao .left-sidebar > ul > li > a {
    color: #ffd800;
    background-color: #853208;
    margin-bottom: 8px;
    padding-left: 15px;
    border-radius: 0 5px 5px 0;
    position: relative
}

.achiver-phatgiao .left-sidebar > ul > li:hover > a {
    color: #fff
}

.achiver-phatgiao .left-sidebar > ul > li > a::before {
    content: '';
    display: inline-block;
    width: 13px;
    height: 7px;
    position: absolute;
    left: 0;
    bottom: -7px;
    background: url(/images/s-detail-bg-1573613130.png) center center no-repeat
}

.achiver-phatgiao .left-sidebar ul.sub-menu {
    margin-bottom: 10px
}

.achiver-phatgiao .left-sidebar ul.sub-menu li {
    border-bottom: 1px dotted #853208
}

.achiver-phatgiao .left-sidebar ul.sub-menu li:last-child {
    border-bottom: none
}

.achiver-phatgiao .post-list .media {
    padding-bottom: 5px;
    border-bottom: 1px dotted #853208
}

.achiver-phatgiao .post-list .media:last-child {
    border-bottom: none
}

.achiver-phatgiao .post-list .media a {
    font-weight: 600;
    font-size: 16px
}

.achiver-phatgiao .post-list .media p {
    font-size: 14px
}

@media (min-width: 768px) {
    .achiver-phatgiao .left-sidebar {
        margin-left: -28px
    }

    .achiver-phatgiao .left-sidebar ul.sub-menu li {
        margin-left: 14px;
        max-width: 93%
    }

    .achiver-phatgiao .left-sidebar ul.sub-menu li a:before {
        content: '\f0da';
        font-family: fontawesome;
        margin-right: 5px
    }

    .achiver-phatgiao .post-list .media img {
        width: 140px;
        height: auto;
        min-height: 100px
    }
}

@media (max-width: 560px) {
    .achiver-phatgiao .post-list .media {
        display: block
    }

    .achiver-phatgiao .post-list .media img {
        width: 100%
    }
}

.achiver-phatgiao .breadcrumb::after, .achiver-phatgiao .breadcrumb::before {
    display: none
}

.achiver-phatgiao h5 {
    margin-top: 0;
    font-weight: normal;
}

.achiver-phatgiao .media h5 {
    margin-bottom: 5px
}

@media (max-width: 576px) {
    .fb-page iframe {
        width: calc(100% - 15px) !important
    }

    .achiver-phatgiao .left-sidebar .navbar-collapse {
        max-width: 94%;
        margin-left: 4%
    }

    .achiver-phatgiao .left-sidebar > ul > li > a {
        line-height: 24px
    }
}

.achiver-phatgiao .left-sidebar .navbar-collapse {
    display: block !important;
    margin-bottom: 10px
}

.achiver-phatgiao .left-sidebar .navbar-collapse a::before {
    content: '\f0da';
    font-family: fontawesome;
    margin-right: 5px
}

.achiver-phatgiao .left-sidebar .navbar-collapse a {
    border-bottom: 1px dotted #853208;
    line-height: 22px
}

.achiver-phatgiao .left-sidebar .navbar-collapse a:last-child {
    border-bottom: none
}

@media (min-width: 768px) {
    .achiver-phatgiao .left-sidebar .navbar-collapse a {
        margin-left: 14px;
        max-width: 93%
    }
}

.achiver-phatgiao .left-sidebar .dropdown-toggle::after {
    float: right;
    margin-right: 15px;
    margin-top: 6px
}

.achiver-phatgiao .left-sidebar .navbar-nav .border-top {
    border-top: none !important
}

.achiver-phatgiao .clock-pc {
    padding-left: 0
}

.achiver-phatgiao .clock-pc::before {
    display: none
}

@media (min-width: 768px) {
    .achiver-phatgiao .clock-pc {
        position: absolute;
        right: 35px
    }
}

.achiver-phatgiao .breadcrumb-item span {
    font-size: 12px;
    line-heigh: 24px
}

.single-phatgiao .container {
    border-radius: 0 0 7px 7px
}

.single-phatgiao a {
    line-height: 18px;
    color: #853208;
    font-size: 14px;
    display: inline-block;
    text-decoration: none
}

.single-phatgiao .container {
    background-color: #fff;
    box-shadow: 0 10px 10px #888
}

.single-phatgiao a:hover {
    color: #d6990c
}

.single-phatgiao .breadcrumb {
    padding: 8px 15px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border-radius: 4px
}

.single-phatgiao .slide img {
    height: 325px
}

.single-phatgiao .widget .card {
    border-radius: 7px;
    border-color: #964609
}

.single-phatgiao .widget li a {
    width: 100%
}

.related-posts .related-posts-title, .single-phatgiao .widget .card-title {
    border-radius: 7px 7px 0 0;
    background: url(/images/s-bg-box-1573550840.jpg) repeat scroll center center;
    padding: 10px 0;
    position: relative;
    margin-left: -1px;
    margin-right: -1px;
    margin-bottom: 0
}

.related-posts .related-posts-title h3, .single-phatgiao .widget .card-title h5 {
    color: #ffd800;
    font-size: 18px;
    margin-bottom: 0
}

.related-posts .related-posts-title .tile-icon, .single-phatgiao .widget .card-title .tile-icon {
    display: inline-block;
    position: absolute;
    height: 42px;
    left: -10px;
    position: absolute;
    top: 0;
    width: 50px;
    background: url(/images/s-icontt-1573552480.png) no-repeat scroll -14px -18px
}

.single-phatgiao .widget .card-body {
    padding: 5px 7px
}

.single-phatgiao .widget ul {
    list-style: none;
    padding-left: 0;
    margin: 0
}

.single-phatgiao .widget ul li a {
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 1px dotted #853208
}

.single-phatgiao .widget ul li:last-child a {
    border-bottom: none;
    padding-bottom: 0
}

.single-phatgiao .widget.widget-2col ul li:last-child a {
    padding-bottom: 5px;
    border-bottom: 1px dotted #853208
}

.single-phatgiao .main-content .widget .media {
    background: #eee;
    margin-left: -7px;
    margin-right: -7px;
    margin-top: -5px;
    padding: 10px 7px 0 7px
}

.single-phatgiao .widget ul li a:before {
    content: '\f0da';
    font-family: fontawesome;
    margin-right: 5px
}

.single-phatgiao .widget .media img {
    width: 110px;
    height: 80px
}

.single-phatgiao .widget .media.video {
    display: block
}

.single-phatgiao .widget .media h5 {
    line-height: 1;
    margin-bottom: 3px
}

.single-phatgiao .related-posts h5 a, .single-phatgiao .widget .media h5 a {
    font-weight: 600;
    font-size: 15px
}

.single-phatgiao .related-posts p, .single-phatgiao .widget .media p {
    font-size: 14px;
    line-height: 1.3
}

.single-phatgiao aside .widget .card-title .tile-icon {
    left: 10%;
    top: -50%;
    background-position-y: -547px;
    background-position-x: -3px
}

.single-phatgiao aside .widget .mau {
    margin-top: 10px;
    clear: both
}

.single-phatgiao aside .widget .infoCurrentDay {
    background: #8b4b0b url(/images/s-lich-1573552517.png) repeat-x right center;
    color: #fd0;
    border-radius: 10px 10px 0 0;
    -moz-border-radius: 10px 10px 0 0
}

.single-phatgiao aside .widget .tcle {
    text-align: center;
    font-size: 13px;
    line-height: 32px
}

.single-phatgiao aside .widget .solar_day {
    text-align: center
}

.single-phatgiao aside .widget .solar_day .number {
    text-align: center;
    font-family: Myriad Pro;
    font-size: 64pt;
    font-weight: 700;
    line-height: 67px
}

.single-phatgiao aside .widget .label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em
}

.single-phatgiao aside .widget #tongcl {
    float: left;
    width: 161px;
    text-align: center
}

.single-phatgiao aside .widget #tongc2 {
    text-align: center
}

.single-phatgiao aside .widget .am {
    font-weight: 500;
    font-size: 9px;
    color: #777;
    line-height: 8px;
    text-align: right
}

.single-phatgiao aside .widget .navi-l, .single-phatgiao aside .widget .navi-r, .single-phatgiao aside .widget .tenthang {
    text-align: center
}

.single-phatgiao .sing-content h1 {
    font-size: 22px;
    font-weight: 600;
    color: #853208
}

.single-phatgiao .sing-content h4.excrept {
    font-size: 16px;
    font-weight: 400;
    background: #eee;
    border-radius: 7px;
    padding: 15px;
    line-height: 1.5
}

.related-posts .related-posts-title h3 {
    margin-left: 60px;
    margin-top: 0;
    font-weight: 400
}

.related-posts .related-posts-list {
    padding: 15px;
    border: 1px solid #964609;
    border-radius: 0 0 7px 7px
}

@media (min-width: 768px) {
    .single-phatgiao .related-posts img {
        max-width: 130px
    }
}

@media (max-width: 576px) {
    .related-posts .related-posts-list .media {
        display: block
    }

    .related-posts .related-posts-list .media img {
        width: 100%
    }
}

.single-phatgiao .clock-pc {
    padding-left: 0;
    width: 100%
}

.single-phatgiao .clock-pc::before {
    display: none
}

.single-phatgiao .breadcrumb-item span {
    font-size: 12px;
    line-heigh: 24px
}

.single-phatgiao .container {
    border-radius: 0 0 7px 7px
}

.single-phatgiao a {
    line-height: 18px;
    color: #853208;
    font-size: 14px;
    display: inline-block;
    text-decoration: none
}

.single-phatgiao .container {
    background-color: #fff;
    box-shadow: 0 10px 10px #888
}

.single-phatgiao a:hover {
    color: #d6990c
}

.single-phatgiao .breadcrumb {
    padding: 8px 15px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border-radius: 4px
}

.single-phatgiao .slide img {
    height: 325px
}

.single-phatgiao .widget .card {
    border-radius: 7px;
    border-color: #964609
}

.single-phatgiao .widget li a {
    width: 100%
}

.related-posts .related-posts-title, .single-phatgiao .widget .card-title {
    border-radius: 7px 7px 0 0;
    background: url(/images/s-bg-box-1573550840.jpg) repeat scroll center center;
    padding: 10px 0;
    position: relative;
    margin-left: -1px;
    margin-right: -1px;
    margin-bottom: 0
}

.related-posts .related-posts-title h3, .single-phatgiao .widget .card-title h5 {
    color: #ffd800;
    font-size: 18px;
    margin-bottom: 0
}

.related-posts .related-posts-title .tile-icon, .single-phatgiao .widget .card-title .tile-icon {
    display: inline-block;
    position: absolute;
    height: 42px;
    left: -10px;
    position: absolute;
    top: 0;
    width: 50px;
    background: url(/images/s-icontt-1573552480.png) no-repeat scroll -14px -18px
}

.single-phatgiao .widget .card-body {
    padding: 5px 7px
}

.single-phatgiao .widget ul {
    list-style: none;
    padding-left: 0;
    margin: 0
}

.single-phatgiao .widget ul li a {
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 1px dotted #853208
}

.single-phatgiao .widget ul li:last-child a {
    border-bottom: none;
    padding-bottom: 0
}

.single-phatgiao .widget.widget-2col ul li:last-child a {
    padding-bottom: 5px;
    border-bottom: 1px dotted #853208
}

.single-phatgiao .main-content .widget .media {
    background: #eee;
    margin-left: -7px;
    margin-right: -7px;
    margin-top: -5px;
    padding: 10px 7px 0 7px
}

.single-phatgiao .widget ul li a:before {
    content: '\f0da';
    font-family: fontawesome;
    margin-right: 5px
}

.single-phatgiao .widget .media img {
    width: 110px;
    height: 80px
}

.single-phatgiao .widget .media.video {
    display: block
}

.single-phatgiao .widget .media h5 {
    line-height: 1;
    margin-bottom: 3px
}

.single-phatgiao .related-posts h5 a, .single-phatgiao .widget .media h5 a {
    font-weight: 600;
    font-size: 15px
}

.single-phatgiao .related-posts p, .single-phatgiao .widget .media p {
    font-size: 14px;
    line-height: 1.3
}

.single-phatgiao aside .widget .card-title .tile-icon {
    left: 10%;
    top: -50%;
    background-position-y: -547px;
    background-position-x: -3px
}

.single-phatgiao aside .widget .mau {
    margin-top: 10px;
    clear: both
}

.single-phatgiao aside .widget .infoCurrentDay {
    background: #8b4b0b url(/images/s-lich-1573552517.png) repeat-x right center;
    color: #fd0;
    border-radius: 10px 10px 0 0;
    -moz-border-radius: 10px 10px 0 0
}

.single-phatgiao aside .widget .tcle {
    text-align: center;
    font-size: 13px;
    line-height: 32px
}

.single-phatgiao aside .widget .solar_day {
    text-align: center
}

.single-phatgiao aside .widget .solar_day .number {
    text-align: center;
    font-family: Myriad Pro;
    font-size: 64pt;
    font-weight: 700;
    line-height: 67px
}

.single-phatgiao aside .widget .label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em
}

.single-phatgiao aside .widget #tongcl {
    float: left;
    width: 161px;
    text-align: center
}

.single-phatgiao aside .widget #tongc2 {
    text-align: center
}

.single-phatgiao aside .widget .am {
    font-weight: 500;
    font-size: 9px;
    color: #777;
    line-height: 8px;
    text-align: right
}

.single-phatgiao aside .widget .navi-l, .single-phatgiao aside .widget .navi-r, .single-phatgiao aside .widget .tenthang {
    text-align: center
}

.single-phatgiao .sing-content h1 {
    font-size: 22px;
    font-weight: 600;
    color: #853208
}

.single-phatgiao .sing-content h4.excrept {
    font-size: 16px;
    font-weight: 400;
    background: #eee;
    border-radius: 7px;
    padding: 15px;
    line-height: 1.5
}

.related-posts .related-posts-title h3 {
    margin-left: 60px;
    margin-top: 0;
    font-weight: 400
}

.related-posts .related-posts-list {
    padding: 15px;
    border: 1px solid #964609;
    border-radius: 0 0 7px 7px
}

@media (min-width: 768px) {
    .single-phatgiao .related-posts img {
        max-width: 130px
    }
}

@media (max-width: 576px) {
    .related-posts .related-posts-list .media {
        display: block
    }

    .related-posts .related-posts-list .media img {
        width: 100%
    }
}

.single-phatgiao .clock-pc {
    padding-left: 0;
    width: 100%
}

.single-phatgiao .clock-pc::before {
    display: none
}

.single-phatgiao .breadcrumb-item span {
    font-size: 12px;
    line-heigh: 24px
}

body {
    background: #ffdea1 url(/images/s-bg-middle-1573550661.jpg) no-repeat scroll center top
}

@media (min-width: 1200px) {
    .container {
        max-width: 1170px
    }
}

.s35 .btop {
    background: #340100 url(/images/s-bg-top-1573550617.jpg) repeat scroll center center;
    color: #ffd800;
    border-bottom: 3px solid #ffd800
}

.s35 h3 {
    font-size: 22px;
    margin-top: 20px;
    margin-bottom: 5px
}

.s35 #timkiem {
    background: transparent url(/images/s-navi-1573550550.png) no-repeat scroll 0 1px;
    height: 55px;
    padding: 7px 10px 0;
    width: 280px;
    margin: 20px auto 0;
    position: relative
}

.s35 #km {
    background: 0 0;
    border: none;
    width: 212px;
    font-size: 13px;
    vertical-align: 2px
}

.s35 #tmkiem {
    background: 0 0;
    border: none;
    cursor: pointer;
    height: 23px;
    width: 34px;
    position: absolute;
    right: 13px
}

@media (max-width: 560px) {
    .s35 h3 {
        font-size: 18px
    }
}

.s35 .main-menu .container {
    background-color: #fff
}

.s35 .main-menu ul li a {
    color: #853208
}

@media (min-width: 992px) {
    .s35 .navbar {
        padding-top: 0
    }

    .s35 .main-menu ul li a.nav-link {
        font-size: 13px;
        display: block;
        padding: 17px 15px 34px;
        text-align: center;
        height: 71px;
        min-width: 86px;
        font-weight: 700
    }

    .s35 .main-menu ul li.active a, .s35 .main-menu ul li:hover a {
        background: rgba(0, 0, 0, 0) url(/images/s-navi-1573550261.png) repeat scroll 8px 0;
        color: #ffd800;
        text-decoration: none
    }

    .s35 .main-menu ul li a.nav-top-am {
        background-position: 0 -79px;
        width: 112px
    }

    .s35 .main-menu ul li a.nav-top-am {
        background-position: 0 -79px;
        width: 112px
    }
}

@media (max-width: 768px) {
    .s35 .navbar {
        background: url(/images/s-bg-box-1573550840.jpg) repeat scroll center center
    }

    .s35 .main-menu .navbar-brand {
        font-size: 16px;
        color: #ffd800;
        text-transform: uppercase
    }

    .s35 .navbar-toggler {
        color: #ffd800;
        font-size: 24px;
        outline: 0
    }

    .s35 h3 {
        font-size: 18px
    }

    .s35 .main-menu ul li a.nav-link {
        color: #ffd800;
        padding-left: 10px;
        border-bottom: 1px dotted #ffd800
    }

    .s35 .main-menu ul li a.nav-link::before {
        content: '\f0da';
        font-family: fontawesome;
        margin-right: 5px
    }

    .s35 .main-menu ul li:last-child a.nav-link {
        border-bottom: none
    }

    .s35 .main-menu ul li.active a.nav-link, .s35 .main-menu ul li:hover a.nav-link {
        color: #fff;
        background: #6f2907;
        border-color: #6f2907
    }
}

.s36 {
    margin-top: 1px
}

.s36 .container {
    border-radius: 0 0 7px 7px
}

.s36 a {
    line-height: 18px;
    color: #853208;
    font-size: 14px;
    display: inline-block;
    text-decoration: none
}

.s36 .container {
    background-color: #fff;
    box-shadow: 0 10px 10px #888
}

.s36 a:hover {
    color: #d6990c
}

.s36 .breadcrumb {
    padding: 8px 15px;
    margin-bottom: 20px;
    background-color: #f5f5f5;
    border-radius: 4px
}

.s36 .breadcrumb::after, .s36 .breadcrumb::before {
    display: none
}

.s36 .slide img {
    height: 325px
}

.s36 .slide .carousel-caption {
    background: rgba(63, 2, 3, .6);
    width: 100%;
    right: 0;
    left: 0;
    bottom: 30px;
    padding: 10px 15px
}

.s36 .slide .carousel-caption a {
    color: #ffd800;
    font-size: 16px;
    text-decoration: none
}

.s36 .slide .carousel-caption:hover a {
    color: #fff
}

.s36 .carousel-indicators {
    margin-bottom: 0
}

.s36 .carousel-indicators li {
    width: 11px;
    height: 11px;
    border-radius: 100%
}

.s36 .carousel-indicators li.active {
    background-color: #853208;
    opacity: 1
}

.s36 .widget .card {
    border-radius: 7px;
    border-color: #964609
}

.s36 .widget li a {
    width: 100%
}

.s36 .widget .card-title {
    border-radius: 7px 7px 0 0;
    background: url(/images/s-bg-box-1573550840.jpg) repeat scroll center center;
    padding: 10px 0;
    position: relative;
    margin-left: -1px;
    margin-right: -1px;
    margin-bottom: 0
}

.s36 .widget .card-title h5 {
    color: #ffd800;
    font-size: 18px;
    margin-bottom: 0
}

.s36 .widget .card-title .tile-icon {
    display: inline-block;
    position: absolute;
    height: 42px;
    left: -10px;
    position: absolute;
    top: 0;
    width: 50px;
    background: url(/images/s-icontt-1573552480.png) no-repeat scroll -14px -18px
}

.s36 .widget .card-body {
    padding: 5px 7px;
    min-height: 270px
}

.s36 .widget ul {
    list-style: none;
    padding-left: 0;
    margin: 0
}

.s36 .widget ul li a {
    margin-bottom: 5px;
    padding-bottom: 5px;
    border-bottom: 1px dotted #853208
}

.s36 .widget ul li:last-child a {
    border-bottom: none;
    padding-bottom: 0
}

.s36 .widget.widget-2col ul li:last-child a {
    padding-bottom: 5px;
    border-bottom: 1px dotted #853208
}

.s36 .main-content .widget .media {
    background: #eee;
    margin-left: -7px;
    margin-right: -7px;
    margin-top: -5px;
    padding: 10px 7px 0 7px
}

.s36 .widget ul li a:before {
    content: '\f0da';
    font-family: fontawesome;
    margin-right: 5px
}

.s36 .widget .media img {
    width: 110px;
    height: auto;
    min-height: 80px
}

.s36 .widget .media.video {
    display: block
}

.s36 .widget .media h5 {
    line-height: 1;
    margin-bottom: 3px
}

.s36 .widget .media h5 a {
    font-weight: 600;
    font-size: 15px
}

.s36 .widget .media p {
    font-size: 14px;
    line-height: 1.3
}

.s36 aside .widget .card-title .tile-icon {
    left: 10%;
    top: -50%;
    background-position-y: -547px;
    background-position-x: -3px
}

.s36 aside .widget .mau {
    margin-top: 10px;
    clear: both
}

.s36 aside .widget .infoCurrentDay {
    background: #8b4b0b url(/images/s-lich-1573552517.png) repeat-x right center;
    color: #fd0;
    border-radius: 10px 10px 0 0;
    -moz-border-radius: 10px 10px 0 0
}

.s36 aside .widget .tcle {
    text-align: center;
    font-size: 13px;
    line-height: 32px
}

.s36 aside .widget .solar_day {
    text-align: center
}

.s36 aside .widget .solar_day .number {
    text-align: center;
    font-family: Myriad Pro;
    font-size: 64pt;
    font-weight: 700;
    line-height: 67px
}

.s36 aside .widget .label {
    display: inline;
    padding: .2em .6em .3em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: .25em
}

.s36 aside .widget #tongcl {
    float: left;
    width: 161px;
    text-align: center
}

.s36 aside .widget #tongc2 {
    text-align: center
}

.s36 aside .widget .am {
    font-weight: 500;
    font-size: 9px;
    color: #777;
    line-height: 8px;
    text-align: right
}

.s36 aside .widget .navi-l, .s36 aside .widget .navi-r, .s36 aside .widget .tenthang {
    text-align: center
}

h5 {
    margin-top: 0;
    font-weight: 400
}

.s36 .breadcrumb-item span {
    font-size: 12px;
    line-heigh: 24px
}

.s36 .carousel {
    position: relative;
    border: 2px solid #853208;
    border-radius: 7px
}

.s36 .carousel img {
    border-radius: 5px
}

.s36 .banner img {
    border-radius: 7px
}

.s37 {
    background: #732e09 url(/images/s-bg-footer-1573553024.png) repeat-x left top scroll;
    color: #ffeac2
}

.s37 footer {
    padding: 15px 0
}

.s37 footer p {
    margin-bottom: 0
}

.s37 footer h3 {
    font-size: 22px
}

.fb_hidden {
   position: absolute;
   top: -10000px;
   z-index: 10001
}

.fb_reposition {
    overflow: hidden;
    position: relative
}

.fb_invisible {
    display: none
}

.fb_reset {
    background: none;
    border: 0;
    border-spacing: 0;
    color: #000;
    cursor: auto;
    direction: ltr;
    font-family: "lucida grande", tahoma, verdana, arial, sans-serif;
    font-size: 11px;
    font-style: normal;
    font-variant: normal;
    font-weight: normal;
    letter-spacing: normal;
    line-height: 1;
    margin: 0;
    overflow: visible;
    padding: 0;
    text-align: left;
    text-decoration: none;
    text-indent: 0;
    text-shadow: none;
    text-transform: none;
    visibility: visible;
    white-space: normal;
    word-spacing: normal
}

.fb_reset > div {
    overflow: hidden
}

@keyframes fb_transform {
    from {
        opacity: 0;
        transform: scale(.95)
    }
    to {
        opacity: 1;
        transform: scale(1)
    }
}

.fb_animate {
    animation: fb_transform .3s forwards
}

.fb_dialog {
    background: rgba(82, 82, 82, .7);
    position: absolute;
    top: -10000px;
    z-index: 10001
}

.fb_dialog_advanced {
    border-radius: 8px;
    padding: 10px
}

.fb_dialog_content {
    background: #fff;
    color: #373737
}

.fb_dialog_close_icon {
    background: url(https://static.xx.fbcdn.net/rsrc.php/v3/yq/r/IE9JII6Z1Ys.png) no-repeat scroll 0 0 transparent;
    cursor: pointer;
    display: block;
    height: 15px;
    position: absolute;
    right: 18px;
    top: 17px;
    width: 15px
}

.fb_dialog_mobile .fb_dialog_close_icon {
    left: 5px;
    right: auto;
    top: 5px
}

.fb_dialog_padding {
    background-color: transparent;
    position: absolute;
    width: 1px;
    z-index: -1
}

.fb_dialog_close_icon:hover {
    background: url(https://static.xx.fbcdn.net/rsrc.php/v3/yq/r/IE9JII6Z1Ys.png) no-repeat scroll 0 -15px transparent
}

.fb_dialog_close_icon:active {
    background: url(https://static.xx.fbcdn.net/rsrc.php/v3/yq/r/IE9JII6Z1Ys.png) no-repeat scroll 0 -30px transparent
}

.fb_dialog_iframe {
    line-height: 0
}

.fb_dialog_content .dialog_title {
    background: #6d84b4;
    border: 1px solid #365899;
    color: #fff;
    font-size: 14px;
    font-weight: bold;
    margin: 0
}

.fb_dialog_content .dialog_title > span {
    background: url(https://static.xx.fbcdn.net/rsrc.php/v3/yd/r/Cou7n-nqK52.gif) no-repeat 5px 50%;
    float: left;
    padding: 5px 0 7px 26px
}

body.fb_hidden {
    height: 100%;
    left: 0;
    margin: 0;
    overflow: visible;
    position: absolute;
    top: -10000px;
    transform: none;
    width: 100%
}

.fb_dialog.fb_dialog_mobile.loading {
    background: url(https://static.xx.fbcdn.net/rsrc.php/v3/ya/r/3rhSv5V8j3o.gif) white no-repeat 50% 50%;
    min-height: 100%;
    min-width: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    z-index: 10001
}

.fb_dialog.fb_dialog_mobile.loading.centered {
    background: none;
    height: auto;
    min-height: initial;
    min-width: initial;
    width: auto
}

.fb_dialog.fb_dialog_mobile.loading.centered #fb_dialog_loader_spinner {
    width: 100%
}

.fb_dialog.fb_dialog_mobile.loading.centered .fb_dialog_content {
    background: none
}

.loading.centered #fb_dialog_loader_close {
    clear: both;
    color: #fff;
    display: block;
    font-size: 18px;
    padding-top: 20px;
}

#fb-root #fb_dialog_ipad_overlay {
    background: rgba(0, 0, 0, .4);
    bottom: 0;
    left: 0;
    min-height: 100%;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    z-index: 10000;
}

#fb-root #fb_dialog_ipad_overlay.hidden {
    display: none;
}

.fb_dialog.fb_dialog_mobile.loading iframe {
    visibility: hidden;
}

.fb_dialog_mobile .fb_dialog_iframe {
    position: sticky;
    top: 0
}

.fb_dialog_content .dialog_header {
    background: linear-gradient(from(#738aba), to(#2c4987));
    border-bottom: 1px solid;
    border-color: #043b87;
    box-shadow: white 0 1px 1px -1px inset;
    color: #fff;
    font: bold 14px Helvetica, sans-serif;
    text-overflow: ellipsis;
    text-shadow: rgba(0, 30, 84, .296875) 0 -1px 0;
    vertical-align: middle;
    white-space: nowrap
}

.fb_dialog_content .dialog_header table {
    height: 43px;
    width: 100%
}

.fb_dialog_content .dialog_header td.header_left {
    font-size: 12px;
    padding-left: 5px;
    vertical-align: middle;
    width: 60px
}

.fb_dialog_content .dialog_header td.header_right {
    font-size: 12px;
    padding-right: 5px;
    vertical-align: middle;
    width: 60px
}

.fb_dialog_content .touchable_button {
    background: linear-gradient(from(#4267B2), to(#2a4887));
    background-clip: padding-box;
    border: 1px solid #29487d;
    border-radius: 3px;
    display: inline-block;
    line-height: 18px;
    margin-top: 3px;
    max-width: 85px;
    padding: 4px 12px;
    position: relative
}

.fb_dialog_content .dialog_header .touchable_button input {
    background: none;
    border: none;
    color: #fff;
    font: bold 12px Helvetica, sans-serif;
    margin: 2px -12px;
    padding: 2px 6px 3px 6px;
    text-shadow: rgba(0, 30, 84, .296875) 0 -1px 0
}

.fb_dialog_content .dialog_header .header_center {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    line-height: 18px;
    text-align: center;
    vertical-align: middle
}

.fb_dialog_content .dialog_content {
    background: url(https://static.xx.fbcdn.net/rsrc.php/v3/y9/r/jKEcVPZFk-2.gif) no-repeat 50% 50%;
    border: 1px solid #4a4a4a;
    border-bottom: 0;
    border-top: 0;
    height: 150px
}

.fb_dialog_content .dialog_footer {
    background: #f5f6f7;
    border: 1px solid #4a4a4a;
    border-top-color: #ccc;
    height: 40px
}

#fb_dialog_loader_close {
    float: left
}

.fb_dialog.fb_dialog_mobile .fb_dialog_close_icon {
    visibility: hidden
}

#fb_dialog_loader_spinner {
    animation: rotateSpinner 1.2s linear infinite;
    background-color: transparent;
    background-image: url(https://static.xx.fbcdn.net/rsrc.php/v3/yD/r/t-wz8gw1xG1.png);
    background-position: 50% 50%;
    background-repeat: no-repeat;
    height: 24px;
    width: 24px
}

@keyframes rotateSpinner {
    0% {
        transform: rotate(0deg)
    }
    100% {
        transform: rotate(360deg)
    }
}

.fb_iframe_widget {
    display: inline-block;
    position: relative
}

.fb_iframe_widget span {
    display: inline-block;
    position: relative;
    text-align: justify
}

.fb_iframe_widget iframe {
    position: absolute
}

.fb_iframe_widget_fluid_desktop, .fb_iframe_widget_fluid_desktop span, .fb_iframe_widget_fluid_desktop iframe {
    max-width: 100%
}

.fb_iframe_widget_fluid_desktop iframe {
    min-width: 220px;
    position: relative
}

.fb_iframe_widget_lift {
    z-index: 1
}

.fb_iframe_widget_fluid {
    display: inline
}

.fb_iframe_widget_fluid span {
    width: 100%
}