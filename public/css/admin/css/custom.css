.cart-form .box-chipo {
    border-top-color: #f86c6b;
}
.cart-form .box {
    position: relative;
    border-radius: 3px;
    background: #ffffff;
    border-top: 3px solid #d2d6de;
    margin-bottom: 20px;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
}
.cart-form .checkout-step {
    /*overflow: hidden;*/
}
.cart-form .checkout-step .cs-step:nth-child(1) {
    z-index: 4;
}
.cart-form .checkout-step .cs-step.active {
    background: #48576f;
}
.cart-form .checkout-step .cs-step.active:after {
    content: "";
    background: url(../../../images/arrowright_active.png) no-repeat;
    float: right;
    width: 19px;
    height: 46px;
    position: absolute;
    z-index: 2;
    top: 0;
    right: -19px;
}

.cart-form .checkout-step .cs-step {
    float: left;
    width: 33.33%;
    min-height: 46px;
    position: relative;
    z-index: 1;
    /*background: #f5f5f5;*/
    line-height: 46px;
    text-align: center;
}
.cart-form .checkout-step .cs-number {
    display: inline-block;
    vertical-align: middle;
    width: 30px;
    height: 30px;
    background: #48576f;
    line-height: 30px;
    text-align: center;
    margin-right: 5px;
    color: #fff;
    -moz-border-radius: 100%;
    -webkit-border-radius: 100%;
    border-radius: 100%;
}

.cart-form .checkout-step .cs-step.active .cs-number {
    background: #f86c6b;
    color: #fff;
}
.cart-form .checkout-step .cs-step.active .cs-name {
    color: #fff;
}

.cart-form .checkout-step .cs-name {
    color: #333;
}

.total-price {
    display: inline-block;
    width: 100%;
    margin-bottom: 10px;
}
.pd10 {
    padding: 10px;
}

.pull-right {
    float: right;
    padding: 3px !important;
}
.list-inline {
    padding-left: 0;
    margin-left: -5px;
    list-style: none;
}
.list-inline>li {
    display: inline-block;
    padding-right: 5px;
    padding-left: 5px;
}

.make-all {
    position: fixed;
    bottom: 0;
    width: 88%;
}

.amt-comment {
    margin-top: 5px;
}

.amt-comment p {
    margin-bottom: 0px !important;
}

.amt-comment p strong.amt-user {
    color: green;
}
.amt-comment p strong.amt-admin {
    color: orangered;
}
ul.pagination {
    margin-top: 10px !important;
}

.box-header .control-label {
    padding: 3px;
}

.summary-box {
    text-align: center;
}
.summary-number {
    font-size: 45px;
    font-weight: bold;
}

#overlay {
    position: fixed;
    display: none;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0,0,0,0.5);
    z-index: 2;
    cursor: pointer;
}

#text_overlay{
    position: absolute;
    top: 50%;
    left: 50%;
    font-size: 30px;
    color: #20a8d8;
    transform: translate(-50%,-50%);
    -ms-transform: translate(-50%,-50%);
}
.sk-cube-grid .sk-cube {
    background-color: #20a8d8 !important;
}

.sk-cube-grid {
    margin: 0px auto !important;
}

.ui-widget {
    font-family: Arial,Helvetica,sans-serif;
    font-size: 1em;
    padding: 0;
}
.ui-widget .ui-widget {
    font-size: 1em;
}
.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
    font-family: Arial,Helvetica,sans-serif;
    font-size: 1em;
}
.ui-widget.ui-widget-content {
    border: 1px solid #c5c5c5;
}
.ui-widget-content {
    border: 1px solid #dddddd;
    background: #ffffff;
    color: #333333;
}
.ui-widget-content a {
    color: #333333;
}
.ui-widget-header {
    border: 1px solid #dddddd;
    background: #e9e9e9;
    color: #333333;
    font-weight: bold;
}
.ui-widget-header a {
    color: #333333;
}

.ui-widget .ui-menu-item {
    list-style: none;
    padding: 10px;
    cursor: pointer;
}

.ui-widget .ui-menu-item:hover {
    background-color: #20a8d8;
    color: white;
}