<!DOCTYPE html>
<head>
    <meta name='robots' content='noindex,nofollow'/>
    <meta http-equiv='Content-Type' content='text/html; charset=utf-8'/>
    <meta name='viewport' content='width=device-width, initial-scale=1'>
    <title>..:: ~/Home ::..</title>
    <link rel='stylesheet' href='https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css'>
    <script src='https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js'></script>
    <script src='https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js'></script>
    <script src='/js/cry.js'></script>
    <style type='text/css'>
        .auto {
            width: 980px;
            margin: 0 auto
        }

        .tright {
            text-align: right
        }

        .tcenter {
            text-align: center
        }

        .center {
            margin: 0 auto;
            float: none
        }

        .left {
            float: left
        }

        .clr10 {
            clear: both;
            height: 10px
        }

        .mt10 {
            margin-top: 10px
        }

        .red {
            color: #f00 !important
        }

        .err {
            border: 1px solid #f00;
            color: #f00
        }

        .right {
            float: right
        }

        .blue {
            color: blue
        }

        .gray {
            color: gray
        }

        .black {
            color: #000
        }

        .white {
            color: #fff
        }

        .orange {
            color: orange
        }

        .yellow {
            color: yellow
        }

        body {
            background: transparent url('/img/bg/7.jpg') repeat scroll center center
        }

        .boxlogin {
            border: 5px solid #fff;
            box-shadow: 0 0 50px #f00;
            color: #fff;
            text-shadow: 2px 2px 5px #000000;
            border-radius: 5px
        }

        .textshadow {
            text-shadow: -3px 2px 5px #fff
        }

        .green {
            color: green
        }

        .cloading {
            background: #999 url('/img/loading.gif') no-repeat scroll center center !important;
            cursor: default;
            pointer-events: none
        }

        .bg0 {
            background-color: #000
        }

        .lowercase {
            text-transform: lowercase
        }
    </style>
</head>
<body>

<div class='row'>
    <div class='clr10'></div>
    <div class='clr10'></div>
    <div class='clr10'></div>
    <div class='clr10'></div>
    <div class='clr10'></div>
    <div class='col-xs-12 col-sm-5 col-md-4 center mt10 boxlogin'>
        <div class='clr10'></div>
        <div class='modal-header'>
            <div class='clr10'></div>
            <h4 class='modal-title tcenter mt10'><span class='textshadow'><b>ĐĂNG NHẬP HỆ THỐNG</b></span></h4>
            <div class='clr10'></div>
            <div class='clr10'></div>
            <div class='clr10'></div>
        </div>
        <div class='modal-body'>
            <div class='form-group'>
                <label for='recipient-mail' class='control-label'>Tên đăng nhập:</label>
                <input type='text' class='form-control chk lowercase' id='recipient-mail'
                       placeholder='Tên đăng nhập của bạn!'/>
            </div>
            <div class='form-group'>
                <label for='recipient-pass' class='control-label'>Mật khẩu:</label>
                <input type='password' class='form-control chk' id='recipient-pass'>
            </div>
        </div>
        <div class='modal-footer'>
            <div id='rErr' class='tcenter'></div>
            <div class='clr10'></div>
            <span class='left mt10'>+ Tìm lại <a class='red' href='/forgotpass'><u><b>mật khẩu</b></u></a>!</span>
            <button type='button' class='btn btn-primary submit' lang='login' onclick='return ilogin();' id='ilogin'>
                Đăng nhập
            </button>
        </div>
        <p class='tcenter' id='elogin'></p>
        <p class='tcenter'><span class='textshadow'>&copy; All right reserved.</span></p>
        <script type='text/javascript'>
            function ilogin() {
                var u = String(cry.MD5($('#recipient-mail').val().toLowerCase())),
                    p = String(cry.MD5($('#recipient-pass').val()));
                var b = true;
                $('.chk').each(function () {
                    if ($(this).val() == '' || !$(this).val()) {
                        $(this).addClass('err');
                        b = false;
                    } else $(this).removeClass('err');
                });
                if (b) {
                    $('#ilogin').attr('disabled', 'disabled').addClass('cloading');
                    $.post('//groot.phatgiaothanhhoa.com/task/', {
                        rt: 'login',
                        u: u,
                        p: p,
                        tk: 'c258c366062c40f76d6d8a09b0cd976a'
                    }, function (d) {
                        if (d['status'] == 1) window.location.reload(); else {
                            if (d['alert'] === 0) window.location.reload();
                            if (d['alert']) {
                                $('#elogin').html("<span class='glyphicon glyphicon-warning-sign red'></span> <span class='label label-warning'>Bạn còn <span class='badge'>" + d['alert'] + "</span> lần đăng nhập sai!</span> <span class='glyphicon glyphicon-warning-sign red'></span>");
                            }
                            $('#rErr').html(d['emsg']);
                            $('#ilogin').removeAttr('disabled').removeClass('cloading');
                        }
                    }, 'json');
                }
                return false;
            } </script>
    </div>
</div>

</body>
</html>
